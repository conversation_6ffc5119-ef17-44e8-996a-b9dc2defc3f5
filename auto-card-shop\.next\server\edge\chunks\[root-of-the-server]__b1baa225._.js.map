{"version": 3, "sources": [], "sections": [{"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib"], "sourcesContent": ["__turbopack_context__.n(__import_unsupported(`fs`));\n"], "names": [], "mappings": "AAAA,sBAAsB,CAAC,CAAC,qBAAqB,CAAC,EAAE,CAAC"}}, {"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib"], "sourcesContent": ["__turbopack_context__.n(__import_unsupported(`path`));\n"], "names": [], "mappings": "AAAA,sBAAsB,CAAC,CAAC,qBAAqB,CAAC,IAAI,CAAC"}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/maintenance.ts"], "sourcesContent": ["import { promises as fs } from 'fs'\nimport path from 'path'\n\nconst MAINTENANCE_FILE = path.join(process.cwd(), 'maintenance.json')\n\ninterface MaintenanceSettings {\n  maintenanceMode: boolean\n  maintenanceMessage: string\n  updatedAt: string\n}\n\n// 检查是否处于维护模式\nexport async function isMaintenanceMode(): Promise<boolean> {\n  try {\n    const data = await fs.readFile(MAINTENANCE_FILE, 'utf8')\n    const settings: MaintenanceSettings = JSON.parse(data)\n    return settings.maintenanceMode\n  } catch (error) {\n    // 如果文件不存在或读取失败，默认不是维护模式\n    return false\n  }\n}\n\n// 获取维护消息\nexport async function getMaintenanceMessage(): Promise<string> {\n  try {\n    const data = await fs.readFile(MAINTENANCE_FILE, 'utf8')\n    const settings: MaintenanceSettings = JSON.parse(data)\n    return settings.maintenanceMessage || '网站正在维护中，请稍后再试。'\n  } catch (error) {\n    return '网站正在维护中，请稍后再试。'\n  }\n}\n\n// 获取完整的维护设置\nexport async function getMaintenanceSettings(): Promise<MaintenanceSettings> {\n  try {\n    const data = await fs.readFile(MAINTENANCE_FILE, 'utf8')\n    return JSON.parse(data)\n  } catch (error) {\n    return {\n      maintenanceMode: false,\n      maintenanceMessage: '网站正在维护中，请稍后再试。',\n      updatedAt: new Date().toISOString()\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGA,MAAM,mBAAmB,4GAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAS3C,eAAe;IACpB,IAAI;QACF,MAAM,OAAO,MAAM,4GAAA,CAAA,WAAE,CAAC,QAAQ,CAAC,kBAAkB;QACjD,MAAM,WAAgC,KAAK,KAAK,CAAC;QACjD,OAAO,SAAS,eAAe;IACjC,EAAE,OAAO,OAAO;QACd,wBAAwB;QACxB,OAAO;IACT;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,OAAO,MAAM,4GAAA,CAAA,WAAE,CAAC,QAAQ,CAAC,kBAAkB;QACjD,MAAM,WAAgC,KAAK,KAAK,CAAC;QACjD,OAAO,SAAS,kBAAkB,IAAI;IACxC,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,OAAO,MAAM,4GAAA,CAAA,WAAE,CAAC,QAAQ,CAAC,kBAAkB;QACjD,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,OAAO;YACL,iBAAiB;YACjB,oBAAoB;YACpB,WAAW,IAAI,OAAO,WAAW;QACnC;IACF;AACF"}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport type { NextRequest } from 'next/server'\nimport { getToken } from 'next-auth/jwt'\nimport { isMaintenanceMode } from '@/lib/maintenance'\n\nexport async function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl\n\n  // 检查维护模式\n  const maintenanceMode = await isMaintenanceMode()\n  \n  if (maintenanceMode) {\n    // 允许访问的路径（管理员相关）\n    const allowedPaths = [\n      '/maintenance',\n      '/auth/signin',\n      '/api/auth',\n      '/admin',\n      '/api/admin'\n    ]\n\n    // 检查是否是允许的路径\n    const isAllowedPath = allowedPaths.some(path => pathname.startsWith(path))\n    \n    if (!isAllowedPath) {\n      // 检查用户是否是管理员\n      const token = await getToken({ req: request })\n      \n      if (!token || token.role !== 'ADMIN') {\n        // 重定向到维护页面\n        return NextResponse.redirect(new URL('/maintenance', request.url))\n      }\n    }\n  }\n\n  // 保护管理员路由\n  if (pathname.startsWith('/admin')) {\n    const token = await getToken({ req: request })\n    \n    if (!token) {\n      return NextResponse.redirect(new URL('/auth/signin', request.url))\n    }\n    \n    if (token.role !== 'ADMIN') {\n      return NextResponse.redirect(new URL('/', request.url))\n    }\n  }\n\n  return NextResponse.next()\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */\n    '/((?!_next/static|_next/image|favicon.ico|public/).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAEA;AACA;;;;AAEO,eAAe,WAAW,OAAoB;IACnD,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,SAAS;IACT,MAAM,kBAAkB,MAAM,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD;IAE9C,IAAI,iBAAiB;QACnB,iBAAiB;QACjB,MAAM,eAAe;YACnB;YACA;YACA;YACA;YACA;SACD;QAED,aAAa;QACb,MAAM,gBAAgB,aAAa,IAAI,CAAC,CAAA,OAAQ,SAAS,UAAU,CAAC;QAEpE,IAAI,CAAC,eAAe;YAClB,aAAa;YACb,MAAM,QAAQ,MAAM,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE;gBAAE,KAAK;YAAQ;YAE5C,IAAI,CAAC,SAAS,MAAM,IAAI,KAAK,SAAS;gBACpC,WAAW;gBACX,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,gBAAgB,QAAQ,GAAG;YAClE;QACF;IACF;IAEA,UAAU;IACV,IAAI,SAAS,UAAU,CAAC,WAAW;QACjC,MAAM,QAAQ,MAAM,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE;YAAE,KAAK;QAAQ;QAE5C,IAAI,CAAC,OAAO;YACV,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,gBAAgB,QAAQ,GAAG;QAClE;QAEA,IAAI,MAAM,IAAI,KAAK,SAAS;YAC1B,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,QAAQ,GAAG;QACvD;IACF;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}