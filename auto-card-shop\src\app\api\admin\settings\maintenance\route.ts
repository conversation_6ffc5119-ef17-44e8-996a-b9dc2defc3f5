import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { promises as fs } from 'fs'
import path from 'path'

interface MaintenanceSettings {
  maintenanceMode: boolean
  maintenanceMessage: string
  updatedAt: string
}

// 读取维护设置
async function readMaintenanceSettings(): Promise<MaintenanceSettings> {
  try {
    // 使用数据库存储维护设置
    const setting = await prisma.setting.findUnique({
      where: { key: 'maintenance' }
    })

    if (setting && setting.value) {
      return JSON.parse(setting.value)
    }
  } catch (error) {
    console.error('读取维护设置失败:', error)
  }

  // 返回默认设置
  return {
    maintenanceMode: false,
    maintenanceMessage: '网站正在维护中，请稍后再试。',
    updatedAt: new Date().toISOString()
  }
}

// 写入维护设置
async function writeMaintenanceSettings(settings: MaintenanceSettings): Promise<void> {
  // 更新数据库
  await prisma.setting.upsert({
    where: { key: 'maintenance' },
    update: { value: JSON.stringify(settings) },
    create: {
      key: 'maintenance',
      value: JSON.stringify(settings)
    }
  })

  // 同时创建/删除维护模式标志文件，供中间件快速检查
  const maintenanceFlagFile = path.join(process.cwd(), '.maintenance')

  try {
    if (settings.maintenanceMode) {
      // 创建维护模式标志文件
      await fs.writeFile(maintenanceFlagFile, JSON.stringify(settings), 'utf8')
    } else {
      // 删除维护模式标志文件
      try {
        await fs.unlink(maintenanceFlagFile)
      } catch (error) {
        // 文件不存在时忽略错误
      }
    }
  } catch (error) {
    console.error('更新维护模式标志文件失败:', error)
  }
}

export async function GET(request: NextRequest) {
  try {
    // 维护模式状态对所有用户开放（只读）
    const settings = await readMaintenanceSettings()
    return NextResponse.json(settings)

  } catch (error) {
    console.error('获取维护设置失败:', error)
    return NextResponse.json({ error: '获取设置失败' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: '未授权' }, { status: 401 })
    }

    const { maintenanceMode, maintenanceMessage } = await request.json()

    // 验证输入
    if (typeof maintenanceMode !== 'boolean') {
      return NextResponse.json({ error: '维护模式状态必须是布尔值' }, { status: 400 })
    }

    if (!maintenanceMessage || typeof maintenanceMessage !== 'string') {
      return NextResponse.json({ error: '维护消息不能为空' }, { status: 400 })
    }

    const settings: MaintenanceSettings = {
      maintenanceMode,
      maintenanceMessage: maintenanceMessage.trim(),
      updatedAt: new Date().toISOString()
    }

    await writeMaintenanceSettings(settings)

    return NextResponse.json({ 
      success: true, 
      message: '维护设置保存成功',
      settings 
    })

  } catch (error) {
    console.error('保存维护设置失败:', error)
    return NextResponse.json({ error: '保存设置失败' }, { status: 500 })
  }
}
