{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number) {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(price)\n}\n\nexport function formatDate(date: string | Date) {\n  return new Intl.DateTimeFormat('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/app/admin/settings/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Save, Settings, AlertTriangle, CheckCircle } from 'lucide-react'\n\nexport default function SystemSettings() {\n  const [loading, setLoading] = useState(false)\n  const [maintenanceMode, setMaintenanceMode] = useState(false)\n  const [maintenanceMessage, setMaintenanceMessage] = useState('网站正在维护中，请稍后再试。')\n\n  useEffect(() => {\n    fetchMaintenanceSettings()\n  }, [])\n\n  const fetchMaintenanceSettings = async () => {\n    try {\n      const response = await fetch('/api/admin/settings/maintenance')\n      if (response.ok) {\n        const data = await response.json()\n        setMaintenanceMode(data.maintenanceMode || false)\n        setMaintenanceMessage(data.maintenanceMessage || '网站正在维护中，请稍后再试。')\n      }\n    } catch (error) {\n      console.error('获取维护设置失败:', error)\n    }\n  }\n\n  const handleSaveMaintenanceSettings = async () => {\n    setLoading(true)\n\n    try {\n      const response = await fetch('/api/admin/settings/maintenance', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          maintenanceMode,\n          maintenanceMessage\n        }),\n      })\n\n      if (response.ok) {\n        alert('维护设置保存成功')\n      } else {\n        alert('保存失败')\n      }\n    } catch (error) {\n      alert('保存失败，请重试')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h1 className=\"text-2xl font-bold text-gray-900\">系统设置</h1>\n        <p className=\"text-gray-600\">管理网站维护模式</p>\n      </div>\n\n      {/* 维护模式设置 */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <div className=\"flex items-center mb-6\">\n          <Settings className=\"w-6 h-6 text-blue-600 mr-3\" />\n          <h2 className=\"text-lg font-semibold text-gray-900\">维护模式</h2>\n        </div>\n\n        {/* 当前状态 */}\n        <div className=\"mb-6 p-4 rounded-lg border-2 border-dashed\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              {maintenanceMode ? (\n                <AlertTriangle className=\"w-5 h-5 text-orange-500 mr-2\" />\n              ) : (\n                <CheckCircle className=\"w-5 h-5 text-green-500 mr-2\" />\n              )}\n              <span className=\"font-medium\">\n                当前状态: {maintenanceMode ? '维护模式已启用' : '网站正常运行'}\n              </span>\n            </div>\n            <div className={`px-3 py-1 rounded-full text-sm font-medium ${\n              maintenanceMode\n                ? 'bg-orange-100 text-orange-800'\n                : 'bg-green-100 text-green-800'\n            }`}>\n              {maintenanceMode ? '维护中' : '正常'}\n            </div>\n          </div>\n          {maintenanceMode && (\n            <p className=\"mt-2 text-sm text-gray-600\">\n              网站当前处于维护模式，访客将看到维护页面\n            </p>\n          )}\n        </div>\n\n        <div className=\"space-y-6\">\n          {/* 维护模式开关 */}\n          <div>\n            <label className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                checked={maintenanceMode}\n                onChange={(e) => setMaintenanceMode(e.target.checked)}\n                className=\"h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n              />\n              <span className=\"ml-3 text-sm font-medium text-gray-900\">\n                启用维护模式\n              </span>\n            </label>\n            <p className=\"mt-2 text-sm text-gray-500\">\n              启用后，网站将显示维护页面，只有管理员可以正常访问\n            </p>\n          </div>\n\n          {/* 维护消息设置 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              维护页面消息\n            </label>\n            <textarea\n              rows={4}\n              value={maintenanceMessage}\n              onChange={(e) => setMaintenanceMessage(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n              placeholder=\"请输入维护页面显示的消息...\"\n            />\n            <p className=\"mt-1 text-sm text-gray-500\">\n              此消息将显示在维护页面上，告知访客网站维护的原因\n            </p>\n          </div>\n\n          {/* 维护模式说明 */}\n          <div className=\"bg-blue-50 border border-blue-200 rounded-md p-4\">\n            <h3 className=\"text-sm font-medium text-blue-800 mb-2\">维护模式说明</h3>\n            <ul className=\"text-sm text-blue-700 space-y-1\">\n              <li>• 启用维护模式后，普通用户将无法访问网站</li>\n              <li>• 管理员仍可正常登录和管理网站</li>\n              <li>• 维护页面将显示自定义消息</li>\n              <li>• 建议在系统更新或重要维护时启用</li>\n            </ul>\n          </div>\n\n          {/* 保存按钮 */}\n          <div className=\"flex justify-end\">\n            <Button\n              onClick={handleSaveMaintenanceSettings}\n              disabled={loading}\n              className=\"px-6\"\n            >\n              <Save className=\"w-4 h-4 mr-2\" />\n              {loading ? '保存中...' : '保存设置'}\n            </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,2BAA2B;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,mBAAmB,KAAK,eAAe,IAAI;gBAC3C,sBAAsB,KAAK,kBAAkB,IAAI;YACnD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,MAAM,gCAAgC;QACpC,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mCAAmC;gBAC9D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;gBACF;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;YACR,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAI/B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;kCAItD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CACZ,gCACC,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;qEAEzB,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DAEzB,8OAAC;gDAAK,WAAU;;oDAAc;oDACrB,kBAAkB,YAAY;;;;;;;;;;;;;kDAGzC,8OAAC;wCAAI,WAAW,CAAC,2CAA2C,EAC1D,kBACI,kCACA,+BACJ;kDACC,kBAAkB,QAAQ;;;;;;;;;;;;4BAG9B,iCACC,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAM9C,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDACC,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,OAAO;gDACpD,WAAU;;;;;;0DAEZ,8OAAC;gDAAK,WAAU;0DAAyC;;;;;;;;;;;;kDAI3D,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAM5C,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAM;wCACN,OAAO;wCACP,UAAU,CAAC,IAAM,sBAAsB,EAAE,MAAM,CAAC,KAAK;wCACrD,WAAU;wCACV,aAAY;;;;;;kDAEd,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAM5C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAKR,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,UAAU,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC", "debugId": null}}]}