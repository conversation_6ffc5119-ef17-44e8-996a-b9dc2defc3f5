{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/app/admin/layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\nimport Link from 'next/link'\nimport {\n  Package,\n  ShoppingCart,\n  CreditCard,\n  Users,\n  Settings,\n  Home,\n  Folder\n} from 'lucide-react'\n\nexport default function AdminLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (status === 'loading') return\n    \n    if (!session) {\n      router.push('/auth/signin')\n      return\n    }\n\n    if (session.user.role !== 'ADMIN') {\n      router.push('/')\n      return\n    }\n  }, [session, status, router])\n\n  if (status === 'loading') {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50\">\n        <div className=\"text-center\">\n          <div className=\"inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white gradient-bg\">\n            <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n            </svg>\n            加载中...\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  if (!session || session.user.role !== 'ADMIN') {\n    return null\n  }\n\n  const navigation = [\n    { name: '仪表板', href: '/admin', icon: Home },\n    { name: '分类管理', href: '/admin/categories', icon: Folder },\n    { name: '商品管理', href: '/admin/products', icon: Package },\n    { name: '订单管理', href: '/admin/orders', icon: ShoppingCart },\n    { name: '卡密管理', href: '/admin/cards', icon: CreditCard },\n    { name: '设置', href: '/admin/settings', icon: Settings },\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50\">\n      <div className=\"flex flex-col lg:flex-row\">\n        {/* 移动端顶部导航 */}\n        <div className=\"lg:hidden bg-white/80 backdrop-blur-md shadow-elegant border-b border-white/20 sticky top-0 z-50\">\n          <div className=\"p-4\">\n            <div className=\"flex items-center justify-between\">\n              <h1 className=\"text-lg font-bold gradient-text\">✨ 管理后台</h1>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-purple-400 to-blue-400 rounded-full flex items-center justify-center text-white text-sm font-bold\">\n                  {session.user.username?.charAt(0).toUpperCase()}\n                </div>\n              </div>\n            </div>\n          </div>\n          {/* 移动端导航菜单 */}\n          <div className=\"px-4 pb-4\">\n            <div className=\"grid grid-cols-2 xs:grid-cols-4 gap-2\">\n              {navigation.slice(1).map((item) => {\n                const Icon = item.icon\n                const isActive = typeof window !== 'undefined' && window.location.pathname === item.href\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`flex flex-col items-center p-3 rounded-lg text-xs font-medium transition-all duration-200 hover:bg-white/60 ${\n                      isActive\n                        ? 'text-blue-600 font-semibold'\n                        : 'text-gray-700 hover:text-gray-900'\n                    }`}\n                  >\n                    <Icon className=\"w-5 h-5 mb-1\" />\n                    <span className=\"text-center\">{item.name}</span>\n                  </Link>\n                )\n              })}\n            </div>\n          </div>\n        </div>\n\n        {/* 桌面端侧边栏 - 固定位置 */}\n        <div className=\"hidden lg:block w-64 bg-white/90 backdrop-blur-md shadow-elegant border-r border-white/20 fixed left-0 top-0 h-full z-30 overflow-y-auto\">\n          <div className=\"p-6 border-b border-gray-100 sticky top-0 bg-white/90 backdrop-blur-md\">\n            <h1 className=\"text-xl font-bold gradient-text\">✨ 管理后台</h1>\n          </div>\n          <nav className=\"mt-6 px-3 pb-6\">\n            {navigation.map((item) => {\n              const Icon = item.icon\n              const isActive = typeof window !== 'undefined' && window.location.pathname === item.href\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`flex items-center px-4 py-3 mb-2 rounded-lg text-sm font-medium transition-all duration-200 hover:bg-white/60 ${\n                    isActive\n                      ? 'text-blue-600 font-semibold'\n                      : 'text-gray-700 hover:text-gray-900'\n                  }`}\n                >\n                  <Icon className=\"w-5 h-5 mr-3\" />\n                  {item.name}\n                </Link>\n              )\n            })}\n          </nav>\n        </div>\n\n        {/* 主内容区 - 为固定侧边栏留出空间 */}\n        <div className=\"flex-1 min-w-0 lg:ml-64\">\n          {/* 桌面端头部 */}\n          <header className=\"hidden lg:block bg-white/90 backdrop-blur-md shadow-elegant border-b border-white/20 sticky top-0 z-20\">\n            <div className=\"px-6 py-4\">\n              <div className=\"flex items-center justify-between\">\n                <h2 className=\"text-lg font-semibold text-gray-800\">\n                  欢迎回来, <span className=\"gradient-text\">{session.user.username}</span>\n                </h2>\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"hidden md:block text-sm text-gray-600 bg-white/50 px-3 py-1 rounded-full\">\n                    {session.user.email}\n                  </div>\n                  <div className=\"w-8 h-8 bg-gradient-to-r from-purple-400 to-blue-400 rounded-full flex items-center justify-center text-white text-sm font-bold\">\n                    {session.user.username?.charAt(0).toUpperCase()}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </header>\n\n          <main className=\"p-4 lg:p-6 min-h-screen\">\n            <div className=\"animate-fade-in\">\n              {children}\n            </div>\n          </main>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAgBe,SAAS,YAAY,EAClC,QAAQ,EAGT;IACC,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,WAAW;QAE1B,IAAI,CAAC,SAAS;YACZ,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YACjC,OAAO,IAAI,CAAC;YACZ;QACF;IACF,GAAG;QAAC;QAAS;QAAQ;KAAO;IAE5B,IAAI,WAAW,WAAW;QACxB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;4BAA6C,OAAM;4BAA6B,MAAK;4BAAO,SAAQ;;8CACjH,8OAAC;oCAAO,WAAU;oCAAa,IAAG;oCAAK,IAAG;oCAAK,GAAE;oCAAK,QAAO;oCAAe,aAAY;;;;;;8CACxF,8OAAC;oCAAK,WAAU;oCAAa,MAAK;oCAAe,GAAE;;;;;;;;;;;;wBAC/C;;;;;;;;;;;;;;;;;IAMhB;IAEA,IAAI,CAAC,WAAW,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;QAC7C,OAAO;IACT;IAEA,MAAM,aAAa;QACjB;YAAE,MAAM;YAAO,MAAM;YAAU,MAAM,mMAAA,CAAA,OAAI;QAAC;QAC1C;YAAE,MAAM;YAAQ,MAAM;YAAqB,MAAM,sMAAA,CAAA,SAAM;QAAC;QACxD;YAAE,MAAM;YAAQ,MAAM;YAAmB,MAAM,wMAAA,CAAA,UAAO;QAAC;QACvD;YAAE,MAAM;YAAQ,MAAM;YAAiB,MAAM,sNAAA,CAAA,eAAY;QAAC;QAC1D;YAAE,MAAM;YAAQ,MAAM;YAAgB,MAAM,kNAAA,CAAA,aAAU;QAAC;QACvD;YAAE,MAAM;YAAM,MAAM;YAAmB,MAAM,0MAAA,CAAA,WAAQ;QAAC;KACvD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAkC;;;;;;kDAChD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACZ,QAAQ,IAAI,CAAC,QAAQ,EAAE,OAAO,GAAG;;;;;;;;;;;;;;;;;;;;;;sCAM1C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,WAAW,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;oCACxB,MAAM,OAAO,KAAK,IAAI;oCACtB,MAAM,WAAW,gBAAkB,eAAe,OAAO,QAAQ,CAAC,QAAQ,KAAK,KAAK,IAAI;oCACxF,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,4GAA4G,EACtH,6EAEI,qCACJ;;0DAEF,8OAAC;gDAAK,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAe,KAAK,IAAI;;;;;;;uCATnC,KAAK,IAAI;;;;;gCAYpB;;;;;;;;;;;;;;;;;8BAMN,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAAkC;;;;;;;;;;;sCAElD,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,gBAAkB,eAAe,OAAO,QAAQ,CAAC,QAAQ,KAAK,KAAK,IAAI;gCACxF,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,8GAA8G,EACxH,6EAEI,qCACJ;;sDAEF,8OAAC;4CAAK,WAAU;;;;;;wCACf,KAAK,IAAI;;mCATL,KAAK,IAAI;;;;;4BAYpB;;;;;;;;;;;;8BAKJ,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAO,WAAU;sCAChB,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDAAsC;8DAC5C,8OAAC;oDAAK,WAAU;8DAAiB,QAAQ,IAAI,CAAC,QAAQ;;;;;;;;;;;;sDAE9D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,QAAQ,IAAI,CAAC,KAAK;;;;;;8DAErB,8OAAC;oDAAI,WAAU;8DACZ,QAAQ,IAAI,CAAC,QAAQ,EAAE,OAAO,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5C,8OAAC;4BAAK,WAAU;sCACd,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}]}