(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__b1baa225._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/src/lib [middleware-edge] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
__turbopack_context__.n(__import_unsupported(`fs`));
}}),
"[project]/src/lib [middleware-edge] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
__turbopack_context__.n(__import_unsupported(`path`));
}}),
"[project]/src/lib/maintenance.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getMaintenanceMessage": (()=>getMaintenanceMessage),
    "getMaintenanceSettings": (()=>getMaintenanceSettings),
    "isMaintenanceMode": (()=>isMaintenanceMode)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib [middleware-edge] (ecmascript)");
;
;
const MAINTENANCE_FILE = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].join(process.cwd(), 'maintenance.json');
async function isMaintenanceMode() {
    try {
        const data = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["promises"].readFile(MAINTENANCE_FILE, 'utf8');
        const settings = JSON.parse(data);
        return settings.maintenanceMode;
    } catch (error) {
        // 如果文件不存在或读取失败，默认不是维护模式
        return false;
    }
}
async function getMaintenanceMessage() {
    try {
        const data = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["promises"].readFile(MAINTENANCE_FILE, 'utf8');
        const settings = JSON.parse(data);
        return settings.maintenanceMessage || '网站正在维护中，请稍后再试。';
    } catch (error) {
        return '网站正在维护中，请稍后再试。';
    }
}
async function getMaintenanceSettings() {
    try {
        const data = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["promises"].readFile(MAINTENANCE_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        return {
            maintenanceMode: false,
            maintenanceMessage: '网站正在维护中，请稍后再试。',
            updatedAt: new Date().toISOString()
        };
    }
}
}}),
"[project]/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "middleware": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$jwt$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/jwt/index.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$maintenance$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/maintenance.ts [middleware-edge] (ecmascript)");
;
;
;
async function middleware(request) {
    const { pathname } = request.nextUrl;
    // 检查维护模式
    const maintenanceMode = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$maintenance$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["isMaintenanceMode"])();
    if (maintenanceMode) {
        // 允许访问的路径（管理员相关）
        const allowedPaths = [
            '/maintenance',
            '/auth/signin',
            '/api/auth',
            '/admin',
            '/api/admin'
        ];
        // 检查是否是允许的路径
        const isAllowedPath = allowedPaths.some((path)=>pathname.startsWith(path));
        if (!isAllowedPath) {
            // 检查用户是否是管理员
            const token = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$jwt$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["getToken"])({
                req: request
            });
            if (!token || token.role !== 'ADMIN') {
                // 重定向到维护页面
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/maintenance', request.url));
            }
        }
    }
    // 保护管理员路由
    if (pathname.startsWith('/admin')) {
        const token = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$jwt$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["getToken"])({
            req: request
        });
        if (!token) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/auth/signin', request.url));
        }
        if (token.role !== 'ADMIN') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/', request.url));
        }
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
}
const config = {
    matcher: [
        /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */ '/((?!_next/static|_next/image|favicon.ico|public/).*)'
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__b1baa225._.js.map