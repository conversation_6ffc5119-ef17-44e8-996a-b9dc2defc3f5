'use client'

import React from 'react'
import { But<PERSON> } from './button'
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react'
import { cn } from '@/lib/utils'

interface PaginationProps {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  showSizeChanger?: boolean
  pageSize?: number
  onPageSizeChange?: (size: number) => void
  pageSizeOptions?: number[]
  className?: string
}

export function Pagination({
  currentPage,
  totalPages,
  onPageChange,
  showSizeChanger = false,
  pageSize = 10,
  onPageSizeChange,
  pageSizeOptions = [10, 20, 50, 100],
  className
}: PaginationProps) {
  // 生成页码数组
  const generatePageNumbers = () => {
    const pages: (number | string)[] = []
    const maxVisiblePages = 7

    if (totalPages <= maxVisiblePages) {
      // 如果总页数小于等于最大可见页数，显示所有页码
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i)
      }
    } else {
      // 总是显示第一页
      pages.push(1)

      if (currentPage <= 4) {
        // 当前页在前面时
        for (let i = 2; i <= 5; i++) {
          pages.push(i)
        }
        pages.push('...')
        pages.push(totalPages)
      } else if (currentPage >= totalPages - 3) {
        // 当前页在后面时
        pages.push('...')
        for (let i = totalPages - 4; i <= totalPages; i++) {
          pages.push(i)
        }
      } else {
        // 当前页在中间时
        pages.push('...')
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
          pages.push(i)
        }
        pages.push('...')
        pages.push(totalPages)
      }
    }

    return pages
  }

  const pageNumbers = generatePageNumbers()

  const handlePrevious = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1)
    }
  }

  const handleNext = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1)
    }
  }

  const handlePageClick = (page: number | string) => {
    if (typeof page === 'number' && page !== currentPage) {
      onPageChange(page)
    }
  }

  if (totalPages <= 1) {
    return null
  }

  return (
    <div className={cn('flex flex-col sm:flex-row items-center justify-between gap-4 p-4 bg-white/50 backdrop-blur-sm rounded-xl border border-gray-200/60 shadow-sm', className)}>
      <div className="flex items-center space-x-3">
        {showSizeChanger && onPageSizeChange && (
          <div className="flex items-center space-x-2 bg-white rounded-lg px-3 py-2 border border-gray-200 shadow-sm">
            <span className="text-sm font-medium text-gray-700">每页</span>
            <select
              value={pageSize}
              onChange={(e) => onPageSizeChange(Number(e.target.value))}
              className="px-2 py-1 border-0 bg-transparent text-sm font-medium text-gray-900 focus:outline-none focus:ring-0 cursor-pointer"
            >
              {pageSizeOptions.map((size) => (
                <option key={size} value={size}>
                  {size}
                </option>
              ))}
            </select>
            <span className="text-sm font-medium text-gray-700">条</span>
          </div>
        )}
      </div>

      <div className="flex items-center space-x-2">
        {/* 上一页按钮 */}
        <Button
          variant="outline"
          size="sm"
          onClick={handlePrevious}
          disabled={currentPage <= 1}
          className={cn(
            "px-3 py-2 h-9 border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200",
            currentPage <= 1 && "opacity-50 cursor-not-allowed hover:bg-white hover:border-gray-200"
          )}
        >
          <ChevronLeft className="w-4 h-4" />
          <span className="ml-1 hidden sm:inline">上一页</span>
        </Button>

        {/* 页码按钮 */}
        <div className="flex items-center space-x-1">
          {pageNumbers.map((page, index) => (
            <React.Fragment key={index}>
              {page === '...' ? (
                <span className="px-3 py-2 text-gray-400 flex items-center justify-center">
                  <MoreHorizontal className="w-4 h-4" />
                </span>
              ) : (
                <Button
                  variant={page === currentPage ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handlePageClick(page)}
                  className={cn(
                    'px-3 py-2 h-9 min-w-[36px] font-medium transition-all duration-200',
                    page === currentPage
                      ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-md hover:from-blue-700 hover:to-blue-800 border-blue-600'
                      : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50 text-gray-700 hover:text-blue-700'
                  )}
                >
                  {page}
                </Button>
              )}
            </React.Fragment>
          ))}
        </div>

        {/* 下一页按钮 */}
        <Button
          variant="outline"
          size="sm"
          onClick={handleNext}
          disabled={currentPage >= totalPages}
          className={cn(
            "px-3 py-2 h-9 border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200",
            currentPage >= totalPages && "opacity-50 cursor-not-allowed hover:bg-white hover:border-gray-200"
          )}
        >
          <span className="mr-1 hidden sm:inline">下一页</span>
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>

      <div className="text-sm font-medium text-gray-600 bg-white rounded-lg px-3 py-2 border border-gray-200 shadow-sm">
        第 <span className="text-blue-600 font-semibold">{currentPage}</span> 页，共 <span className="text-blue-600 font-semibold">{totalPages}</span> 页
      </div>
    </div>
  )
}

// 分页信息组件
export function PaginationInfo({
  currentPage,
  pageSize,
  total,
  className
}: {
  currentPage: number
  pageSize: number
  total: number
  className?: string
}) {
  const start = (currentPage - 1) * pageSize + 1
  const end = Math.min(currentPage * pageSize, total)

  return (
    <div className={cn('flex items-center space-x-2 text-sm font-medium text-gray-600 bg-white/80 backdrop-blur-sm rounded-lg px-4 py-2 border border-gray-200/60 shadow-sm', className)}>
      <span>显示第</span>
      <span className="text-blue-600 font-semibold">{start}</span>
      <span>到</span>
      <span className="text-blue-600 font-semibold">{end}</span>
      <span>条记录，共</span>
      <span className="text-blue-600 font-semibold">{total}</span>
      <span>条</span>
    </div>
  )
}
