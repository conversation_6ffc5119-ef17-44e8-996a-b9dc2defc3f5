'use client'

import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Pagination, PaginationInfo } from '@/components/ui/pagination'
import { formatDate } from '@/lib/utils'
import { Plus, Upload, Download, Filter } from 'lucide-react'

interface Card {
  id: string
  cardData: string
  status: string
  orderId: string | null
  usedAt: string | null
  createdAt: string
  product: {
    name: string
  }
}

interface Product {
  id: string
  name: string
  _count: {
    cards: number
  }
}

export default function CardsManagement() {
  const [cards, setCards] = useState<Card[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0
  })

  // 表单状态
  const [formData, setFormData] = useState({
    productId: '',
    cardsText: ''
  })

  useEffect(() => {
    fetchCards()
    fetchProducts()
  }, [])

  useEffect(() => {
    fetchCards()
  }, [selectedProduct, selectedStatus, currentPage, pageSize])

  useEffect(() => {
    setCurrentPage(1) // 重置到第一页
  }, [selectedProduct, selectedStatus])

  const fetchCards = async () => {
    try {
      const params = new URLSearchParams()
      if (selectedProduct) params.append('productId', selectedProduct)
      if (selectedStatus) params.append('status', selectedStatus)
      params.append('page', currentPage.toString())
      params.append('pageSize', pageSize.toString())

      const response = await fetch(`/api/cards?${params}`)
      const data = await response.json()
      setCards(data.cards || [])
      setPagination(data.pagination || {
        page: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0
      })
    } catch (error) {
      console.error('获取卡密失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchProducts = async () => {
    try {
      const response = await fetch('/api/products')
      const data = await response.json()
      setProducts(data)
    } catch (error) {
      console.error('获取商品失败:', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.productId || !formData.cardsText.trim()) {
      alert('请选择商品并输入卡密')
      return
    }

    const cardsArray = formData.cardsText
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)

    if (cardsArray.length === 0) {
      alert('请输入有效的卡密')
      return
    }

    try {
      const response = await fetch('/api/cards', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: formData.productId,
          cards: cardsArray
        }),
      })

      if (response.ok) {
        const result = await response.json()
        await fetchCards()
        await fetchProducts()
        setFormData({ productId: '', cardsText: '' })
        setShowAddForm(false)
        alert(result.message)
      } else {
        const error = await response.json()
        alert(error.error || '添加失败')
      }
    } catch (error) {
      alert('添加失败，请重试')
    }
  }

  const handleBulkDelete = async (status: string) => {
    const message = status === 'AVAILABLE' 
      ? '确定要删除所有可用卡密吗？' 
      : '确定要删除所有已使用卡密吗？'
    
    if (!confirm(message)) return

    try {
      const response = await fetch(`/api/cards/bulk-delete`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status, productId: selectedProduct }),
      })

      if (response.ok) {
        await fetchCards()
        await fetchProducts()
        alert('批量删除成功')
      } else {
        alert('删除失败')
      }
    } catch (error) {
      alert('删除失败，请重试')
    }
  }

  const exportCards = () => {
    if (cards.length === 0) {
      alert('没有卡密可导出')
      return
    }

    let content = '商品名称,卡密,状态,订单号,使用时间,创建时间\n'
    
    cards.forEach(card => {
      content += `"${card.product.name}","${card.cardData}","${getStatusText(card.status)}","${card.orderId || ''}","${card.usedAt ? formatDate(card.usedAt) : ''}","${formatDate(card.createdAt)}"\n`
    })

    const blob = new Blob([content], { type: 'text/csv;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `cards-${new Date().toISOString().split('T')[0]}.csv`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handlePageSizeChange = (size: number) => {
    setPageSize(size)
    setCurrentPage(1)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'AVAILABLE':
        return 'bg-green-100 text-green-800'
      case 'SOLD':
        return 'bg-blue-100 text-blue-800'
      case 'RESERVED':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'AVAILABLE':
        return '可用'
      case 'SOLD':
        return '已售'
      case 'RESERVED':
        return '预留'
      default:
        return status
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">加载中...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">卡密管理</h1>
          <p className="text-gray-600">管理所有商品的卡密库存</p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline" onClick={exportCards}>
            <Download className="w-4 h-4 mr-2" />
            导出卡密
          </Button>
          <Button onClick={() => setShowAddForm(true)}>
            <Plus className="w-4 h-4 mr-2" />
            批量添加
          </Button>
        </div>
      </div>

      {/* 筛选器 */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex flex-wrap gap-4 items-center">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              商品筛选
            </label>
            <select
              value={selectedProduct}
              onChange={(e) => setSelectedProduct(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">所有商品</option>
              {products.map((product) => (
                <option key={product.id} value={product.id}>
                  {product.name} ({product._count.cards} 张)
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              状态筛选
            </label>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">所有状态</option>
              <option value="AVAILABLE">可用</option>
              <option value="SOLD">已售</option>
              <option value="RESERVED">预留</option>
            </select>
          </div>

          <div className="flex space-x-2 mt-6">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBulkDelete('AVAILABLE')}
              disabled={!cards.some(card => card.status === 'AVAILABLE')}
            >
              删除可用卡密
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBulkDelete('SOLD')}
              disabled={!cards.some(card => card.status === 'SOLD')}
            >
              清理已售卡密
            </Button>
          </div>
        </div>
      </div>

      {/* 批量添加表单 */}
      {showAddForm && (
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold mb-4">批量添加卡密</h2>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                选择商品 *
              </label>
              <select
                required
                value={formData.productId}
                onChange={(e) => setFormData({ ...formData, productId: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">选择商品</option>
                {products.map((product) => (
                  <option key={product.id} value={product.id}>
                    {product.name}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                卡密内容 * (每行一个卡密)
              </label>
              <textarea
                required
                rows={10}
                value={formData.cardsText}
                onChange={(e) => setFormData({ ...formData, cardsText: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="请输入卡密，每行一个&#10;例如：&#10;CARD-123456789&#10;CARD-987654321&#10;CARD-456789123"
              />
              <div className="text-sm text-gray-500 mt-1">
                当前输入了 {formData.cardsText.split('\n').filter(line => line.trim()).length} 张卡密
              </div>
            </div>
            
            <div className="flex space-x-3">
              <Button type="submit">
                <Upload className="w-4 h-4 mr-2" />
                批量添加
              </Button>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => {
                  setShowAddForm(false)
                  setFormData({ productId: '', cardsText: '' })
                }}
              >
                取消
              </Button>
            </div>
          </form>
        </div>
      )}

      {/* 卡密列表 */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">
              卡密列表 ({cards.length} 张)
            </h3>
            <div className="text-sm text-gray-500">
              可用: {cards.filter(c => c.status === 'AVAILABLE').length} | 
              已售: {cards.filter(c => c.status === 'SOLD').length} | 
              预留: {cards.filter(c => c.status === 'RESERVED').length}
            </div>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  商品
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  卡密
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  订单号
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  使用时间
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  创建时间
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {cards.map((card) => (
                <tr key={card.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {card.product.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                    {card.cardData}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(card.status)}`}>
                      {getStatusText(card.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {card.orderId ? (
                      <span className="font-mono">{card.orderId.slice(0, 8)}...</span>
                    ) : (
                      '-'
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {card.usedAt ? formatDate(card.usedAt) : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(card.createdAt)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* 分页组件 */}
        {cards.length > 0 && (
          <div className="mt-8 space-y-4">
            <PaginationInfo
              currentPage={pagination.page}
              pageSize={pagination.pageSize}
              total={pagination.total}
            />
            <Pagination
              currentPage={pagination.page}
              totalPages={pagination.totalPages}
              onPageChange={handlePageChange}
              showSizeChanger={true}
              pageSize={pagination.pageSize}
              onPageSizeChange={handlePageSizeChange}
              pageSizeOptions={[10, 20, 50, 100]}
            />
          </div>
        )}
      </div>

      {cards.length === 0 && !loading && (
        <div className="text-center py-12">
          <div className="text-gray-500">暂无卡密</div>
        </div>
      )}
    </div>
  )
}
