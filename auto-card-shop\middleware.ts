import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { getToken } from 'next-auth/jwt'



export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // 保护管理员路由
  if (pathname.startsWith('/admin')) {
    const token = await getToken({ req: request })

    if (!token) {
      return NextResponse.redirect(new URL('/auth/signin', request.url))
    }

    if (token.role !== 'ADMIN') {
      return NextResponse.redirect(new URL('/', request.url))
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: ['/admin/:path*'],
}
