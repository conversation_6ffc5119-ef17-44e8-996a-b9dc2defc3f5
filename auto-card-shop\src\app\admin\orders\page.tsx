'use client'

import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Pagination, PaginationInfo } from '@/components/ui/pagination'
import { formatPrice, formatDate } from '@/lib/utils'
import { Eye, Download, RefreshCw, Search } from 'lucide-react'
import Link from 'next/link'

interface Order {
  id: string
  email: string
  status: string
  totalAmount: number
  stripePaymentId: string | null
  createdAt: string
  updatedAt: string
  user?: {
    username: string
    email: string
  }
  orderItems: Array<{
    id: string
    quantity: number
    price: number
    product: {
      name: string
      image: string | null
    }
  }>
}

export default function OrdersManagement() {
  const [orders, setOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(true)
  const [searchEmail, setSearchEmail] = useState('')
  const [searchOrderId, setSearchOrderId] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0
  })

  useEffect(() => {
    fetchOrders()
  }, [])

  // 当筛选条件或分页改变时自动搜索
  useEffect(() => {
    fetchOrders()
  }, [currentPage, pageSize])

  useEffect(() => {
    if (searchEmail || searchOrderId || statusFilter) {
      setCurrentPage(1) // 重置到第一页
      fetchOrders()
    }
  }, [searchEmail, searchOrderId, statusFilter])

  const fetchOrders = async () => {
    try {
      const params = new URLSearchParams()
      if (searchEmail) params.append('email', searchEmail)
      if (searchOrderId) params.append('orderId', searchOrderId)
      if (statusFilter) params.append('status', statusFilter)
      params.append('page', currentPage.toString())
      params.append('pageSize', pageSize.toString())

      const response = await fetch(`/api/orders?${params}`)
      const data = await response.json()
      setOrders(data.orders || [])
      setPagination(data.pagination || {
        page: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0
      })
    } catch (error) {
      console.error('获取订单失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = () => {
    setLoading(true)
    fetchOrders()
  }

  const handleClearSearch = () => {
    setSearchEmail('')
    setSearchOrderId('')
    setStatusFilter('')
    setCurrentPage(1)
    setLoading(true)
    fetchOrders()
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handlePageSizeChange = (size: number) => {
    setPageSize(size)
    setCurrentPage(1)
  }

  const updateOrderStatus = async (orderId: string, newStatus: string) => {
    if (!confirm(`确定要将订单状态更改为 ${getStatusText(newStatus)} 吗？`)) return

    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (response.ok) {
        await fetchOrders()
        alert('订单状态更新成功')
      } else {
        alert('更新失败')
      }
    } catch (error) {
      alert('更新失败，请重试')
    }
  }

  const exportOrders = () => {
    if (orders.length === 0) {
      alert('没有订单可导出')
      return
    }

    let content = '订单号,邮箱,状态,总金额,支付ID,商品信息,创建时间\n'
    
    orders.forEach(order => {
      const products = order.orderItems.map(item => 
        `${item.product.name} x${item.quantity}`
      ).join('; ')
      
      content += `"${order.id}","${order.email}","${getStatusText(order.status)}","${order.totalAmount}","${order.stripePaymentId || ''}","${products}","${formatDate(order.createdAt)}"\n`
    })

    const blob = new Blob([content], { type: 'text/csv;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `orders-${new Date().toISOString().split('T')[0]}.csv`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800'
      case 'PAID':
        return 'bg-blue-100 text-blue-800'
      case 'DELIVERED':
        return 'bg-green-100 text-green-800'
      case 'CANCELLED':
        return 'bg-red-100 text-red-800'
      case 'REFUNDED':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PENDING':
        return '待支付'
      case 'PAID':
        return '已支付'
      case 'DELIVERED':
        return '已交付'
      case 'CANCELLED':
        return '已取消'
      case 'REFUNDED':
        return '已退款'
      default:
        return status
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">加载中...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">订单管理</h1>
          <p className="text-gray-600">查看和管理所有订单</p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline" onClick={exportOrders}>
            <Download className="w-4 h-4 mr-2" />
            导出订单
          </Button>
          <Button variant="outline" onClick={fetchOrders}>
            <RefreshCw className="w-4 h-4 mr-2" />
            刷新
          </Button>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              邮箱搜索
            </label>
            <input
              type="email"
              value={searchEmail}
              onChange={(e) => setSearchEmail(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="输入邮箱地址"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              订单号搜索
            </label>
            <input
              type="text"
              value={searchOrderId}
              onChange={(e) => setSearchOrderId(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="输入订单号"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              状态筛选
            </label>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">所有状态</option>
              <option value="PENDING">待支付</option>
              <option value="PAID">已支付</option>
              <option value="DELIVERED">已交付</option>
              <option value="CANCELLED">已取消</option>
              <option value="REFUNDED">已退款</option>
            </select>
          </div>
          
          <div className="flex space-x-2">
            <Button onClick={handleSearch}>
              <Search className="w-4 h-4 mr-2" />
              搜索
            </Button>
            <Button variant="outline" onClick={handleClearSearch}>
              清空
            </Button>
          </div>
        </div>
      </div>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {['PENDING', 'PAID', 'DELIVERED', 'CANCELLED', 'REFUNDED'].map(status => {
          const count = orders.filter(order => order.status === status).length
          const total = orders.filter(order => order.status === status)
            .reduce((sum, order) => sum + order.totalAmount, 0)
          
          return (
            <div key={status} className="bg-white rounded-lg shadow p-4">
              <div className="text-sm text-gray-600">{getStatusText(status)}</div>
              <div className="text-2xl font-bold text-gray-900">{count}</div>
              <div className="text-sm text-gray-500">{formatPrice(total)}</div>
            </div>
          )
        })}
      </div>

      {/* 订单列表 */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  订单信息
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  客户
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  商品
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  金额
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  时间
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {orders.map((order) => (
                <tr key={order.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900 font-mono">
                        {order.id.slice(0, 8)}...
                      </div>
                      {order.stripePaymentId && (
                        <div className="text-xs text-gray-500">
                          Stripe: {order.stripePaymentId.slice(0, 12)}...
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm text-gray-900">{order.email}</div>
                      {order.user && (
                        <div className="text-xs text-gray-500">{order.user.username}</div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="space-y-1">
                      {order.orderItems.map((item, index) => (
                        <div key={index} className="text-sm text-gray-900">
                          {item.product.name} × {item.quantity}
                        </div>
                      ))}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                    {formatPrice(order.totalAmount)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="space-y-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                        {getStatusText(order.status)}
                      </span>
                      {order.status === 'PAID' && (
                        <div>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => updateOrderStatus(order.id, 'DELIVERED')}
                          >
                            标记已交付
                          </Button>
                        </div>
                      )}
                      {order.status === 'PENDING' && (
                        <div>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => updateOrderStatus(order.id, 'CANCELLED')}
                          >
                            取消订单
                          </Button>
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div>
                      <div>创建: {formatDate(order.createdAt)}</div>
                      {order.updatedAt !== order.createdAt && (
                        <div>更新: {formatDate(order.updatedAt)}</div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    {order.status === 'DELIVERED' && (
                      <Link href={`/orders/${order.id}/cards`}>
                        <Button size="sm" variant="outline">
                          <Eye className="w-4 h-4 mr-1" />
                          查看卡密
                        </Button>
                      </Link>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* 分页组件 */}
        {orders.length > 0 && (
          <div className="mt-6 flex flex-col sm:flex-row justify-between items-center gap-4">
            <PaginationInfo
              currentPage={pagination.page}
              pageSize={pagination.pageSize}
              total={pagination.total}
            />
            <Pagination
              currentPage={pagination.page}
              totalPages={pagination.totalPages}
              onPageChange={handlePageChange}
              showSizeChanger={true}
              pageSize={pagination.pageSize}
              onPageSizeChange={handlePageSizeChange}
              pageSizeOptions={[10, 20, 50, 100]}
            />
          </div>
        )}
      </div>

      {orders.length === 0 && !loading && (
        <div className="text-center py-12">
          <div className="text-gray-500">暂无订单</div>
        </div>
      )}
    </div>
  )
}
