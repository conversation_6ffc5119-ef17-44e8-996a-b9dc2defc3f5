{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/providers.tsx"], "sourcesContent": ["'use client'\n\nimport { SessionProvider } from 'next-auth/react'\n\nexport function Providers({ children }: { children: React.ReactNode }) {\n  return (\n    <SessionProvider>\n      {children}\n    </SessionProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,SAAS,UAAU,EAAE,QAAQ,EAAiC;IACnE,qBACE,8OAAC,8IAAA,CAAA,kBAAe;kBACb;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/MaintenanceCheck.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useSession } from 'next-auth/react'\nimport { useRouter, usePathname } from 'next/navigation'\n\nexport default function MaintenanceCheck() {\n  const [isChecking, setIsChecking] = useState(true)\n  const { data: session } = useSession()\n  const router = useRouter()\n  const pathname = usePathname()\n\n  useEffect(() => {\n    const checkMaintenanceMode = async () => {\n      try {\n        const response = await fetch('/api/admin/settings/maintenance')\n        if (response.ok) {\n          const data = await response.json()\n          \n          if (data.maintenanceMode) {\n            // 允许访问的路径\n            const allowedPaths = [\n              '/maintenance',\n              '/auth/signin',\n              '/admin'\n            ]\n            \n            // 检查当前路径是否在允许列表中\n            const isAllowed = allowedPaths.some(path => pathname.startsWith(path))\n            \n            // 如果是管理员，允许访问所有页面\n            const isAdmin = session?.user?.role === 'ADMIN'\n            \n            if (!isAllowed && !isAdmin) {\n              router.push('/maintenance')\n              return\n            }\n          }\n        }\n      } catch (error) {\n        console.error('检查维护模式失败:', error)\n      } finally {\n        setIsChecking(false)\n      }\n    }\n\n    checkMaintenanceMode()\n  }, [session, pathname, router])\n\n  // 在检查期间显示加载状态\n  if (isChecking) {\n    return (\n      <div className=\"fixed inset-0 bg-white z-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">正在加载...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return null\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,uBAAuB;YAC3B,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAC7B,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAEhC,IAAI,KAAK,eAAe,EAAE;wBACxB,UAAU;wBACV,MAAM,eAAe;4BACnB;4BACA;4BACA;yBACD;wBAED,iBAAiB;wBACjB,MAAM,YAAY,aAAa,IAAI,CAAC,CAAA,OAAQ,SAAS,UAAU,CAAC;wBAEhE,kBAAkB;wBAClB,MAAM,UAAU,SAAS,MAAM,SAAS;wBAExC,IAAI,CAAC,aAAa,CAAC,SAAS;4BAC1B,OAAO,IAAI,CAAC;4BACZ;wBACF;oBACF;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;YAC7B,SAAU;gBACR,cAAc;YAChB;QACF;QAEA;IACF,GAAG;QAAC;QAAS;QAAU;KAAO;IAE9B,cAAc;IACd,IAAI,YAAY;QACd,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,OAAO;AACT", "debugId": null}}]}