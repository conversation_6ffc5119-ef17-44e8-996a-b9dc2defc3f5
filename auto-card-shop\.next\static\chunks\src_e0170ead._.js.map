{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number) {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(price)\n}\n\nexport function formatDate(date: string | Date) {\n  return new Intl.DateTimeFormat('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-elegant hover-lift\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4EACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/content-converter.ts"], "sourcesContent": ["import TurndownService from 'turndown'\n\n// HTML转Markdown的配置\nconst turndownService = new TurndownService({\n  headingStyle: 'atx',\n  hr: '---',\n  bulletListMarker: '-',\n  codeBlockStyle: 'fenced',\n  fence: '```',\n  emDelimiter: '*',\n  strongDelimiter: '**',\n  linkStyle: 'inlined',\n  linkReferenceStyle: 'full',\n})\n\n// 自定义规则\nturndownService.addRule('strikethrough', {\n  filter: ['del', 's', 'strike'],\n  replacement: function (content) {\n    return '~~' + content + '~~'\n  }\n})\n\n// 表格规则\nturndownService.addRule('table', {\n  filter: 'table',\n  replacement: function (content) {\n    return '\\n\\n' + content + '\\n\\n'\n  }\n})\n\nturndownService.addRule('tableRow', {\n  filter: 'tr',\n  replacement: function (content, node) {\n    const borderCells = Array.from(node.childNodes).map(() => '---').join(' | ')\n    const isHeaderRow = node.parentNode?.nodeName === 'THEAD'\n    \n    if (isHeaderRow) {\n      return '| ' + content + ' |\\n| ' + borderCells + ' |'\n    }\n    return '| ' + content + ' |'\n  }\n})\n\nturndownService.addRule('tableCell', {\n  filter: ['th', 'td'],\n  replacement: function (content) {\n    return content.trim() + ' |'\n  }\n})\n\n/**\n * 将HTML转换为Markdown\n */\nexport function htmlToMarkdown(html: string): string {\n  if (!html) return ''\n  \n  try {\n    return turndownService.turndown(html)\n  } catch (error) {\n    console.error('HTML to Markdown conversion failed:', error)\n    return html\n  }\n}\n\n/**\n * 将Markdown转换为HTML（简单实现）\n */\nexport function markdownToHtml(markdown: string): string {\n  if (!markdown) return ''\n  \n  try {\n    let html = markdown\n    \n    // 标题\n    html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>')\n    html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>')\n    html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>')\n    \n    // 粗体和斜体\n    html = html.replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\n    html = html.replace(/\\*(.*?)\\*/g, '<em>$1</em>')\n    \n    // 代码\n    html = html.replace(/`(.*?)`/g, '<code>$1</code>')\n    \n    // 链接\n    html = html.replace(/\\[([^\\]]+)\\]\\(([^)]+)\\)/g, '<a href=\"$2\">$1</a>')\n    \n    // 列表\n    html = html.replace(/^\\s*\\* (.+)$/gm, '<li>$1</li>')\n    html = html.replace(/(<li>.*<\\/li>)/s, '<ul>$1</ul>')\n    \n    html = html.replace(/^\\s*\\d+\\. (.+)$/gm, '<li>$1</li>')\n    html = html.replace(/(<li>.*<\\/li>)/s, '<ol>$1</ol>')\n    \n    // 引用\n    html = html.replace(/^> (.+)$/gm, '<blockquote>$1</blockquote>')\n    \n    // 分隔线\n    html = html.replace(/^---$/gm, '<hr>')\n    \n    // 段落\n    html = html.replace(/\\n\\n/g, '</p><p>')\n    html = '<p>' + html + '</p>'\n    \n    // 清理空段落\n    html = html.replace(/<p><\\/p>/g, '')\n    html = html.replace(/<p>(<h[1-6]>)/g, '$1')\n    html = html.replace(/(<\\/h[1-6]>)<\\/p>/g, '$1')\n    html = html.replace(/<p>(<ul>|<ol>|<blockquote>|<hr>)/g, '$1')\n    html = html.replace(/(<\\/ul>|<\\/ol>|<\\/blockquote>|<hr>)<\\/p>/g, '$1')\n    \n    return html\n  } catch (error) {\n    console.error('Markdown to HTML conversion failed:', error)\n    return markdown\n  }\n}\n\n/**\n * 检测内容格式\n */\nexport function detectContentFormat(content: string): 'html' | 'markdown' | 'plain' {\n  if (!content) return 'plain'\n\n  // 更严格的HTML标签检测\n  const htmlTagRegex = /<\\/?[a-z][\\s\\S]*>/i\n  const hasHtmlTags = htmlTagRegex.test(content)\n\n  // 检测常见的HTML标签\n  const commonHtmlTags = /<\\/?(?:h[1-6]|p|div|span|strong|em|ul|ol|li|table|tr|td|th|blockquote|a|img|br|hr)\\b[^>]*>/i\n\n  if (hasHtmlTags && commonHtmlTags.test(content)) {\n    return 'html'\n  }\n\n  // 检测Markdown语法\n  const markdownPatterns = [\n    /^#{1,6}\\s+/m,           // 标题\n    /\\*\\*.*?\\*\\*/,           // 粗体\n    /\\*.*?\\*/,               // 斜体\n    /\\[.*?\\]\\(.*?\\)/,        // 链接\n    /^[-*+]\\s+/m,            // 无序列表\n    /^\\d+\\.\\s+/m,            // 有序列表\n    /^>\\s+/m,                // 引用\n    /`.*?`/,                 // 行内代码\n    /^```/m,                 // 代码块\n    /^\\|.*\\|.*$/m,           // 表格\n  ]\n\n  if (markdownPatterns.some(pattern => pattern.test(content))) {\n    return 'markdown'\n  }\n\n  return 'plain'\n}\n\n/**\n * 智能转换内容格式\n */\nexport function convertContent(content: string, targetFormat: 'html' | 'markdown'): string {\n  const currentFormat = detectContentFormat(content)\n  \n  if (currentFormat === targetFormat) {\n    return content\n  }\n  \n  if (currentFormat === 'html' && targetFormat === 'markdown') {\n    return htmlToMarkdown(content)\n  }\n  \n  if (currentFormat === 'markdown' && targetFormat === 'html') {\n    return markdownToHtml(content)\n  }\n  \n  // 纯文本转换\n  if (currentFormat === 'plain') {\n    if (targetFormat === 'html') {\n      return '<p>' + content.replace(/\\n\\n/g, '</p><p>').replace(/\\n/g, '<br>') + '</p>'\n    }\n    return content\n  }\n  \n  return content\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,mBAAmB;AACnB,MAAM,kBAAkB,IAAI,+JAAA,CAAA,UAAe,CAAC;IAC1C,cAAc;IACd,IAAI;IACJ,kBAAkB;IAClB,gBAAgB;IAChB,OAAO;IACP,aAAa;IACb,iBAAiB;IACjB,WAAW;IACX,oBAAoB;AACtB;AAEA,QAAQ;AACR,gBAAgB,OAAO,CAAC,iBAAiB;IACvC,QAAQ;QAAC;QAAO;QAAK;KAAS;IAC9B,aAAa,SAAU,OAAO;QAC5B,OAAO,OAAO,UAAU;IAC1B;AACF;AAEA,OAAO;AACP,gBAAgB,OAAO,CAAC,SAAS;IAC/B,QAAQ;IACR,aAAa,SAAU,OAAO;QAC5B,OAAO,SAAS,UAAU;IAC5B;AACF;AAEA,gBAAgB,OAAO,CAAC,YAAY;IAClC,QAAQ;IACR,aAAa,SAAU,OAAO,EAAE,IAAI;QAClC,MAAM,cAAc,MAAM,IAAI,CAAC,KAAK,UAAU,EAAE,GAAG,CAAC,IAAM,OAAO,IAAI,CAAC;QACtE,MAAM,cAAc,KAAK,UAAU,EAAE,aAAa;QAElD,IAAI,aAAa;YACf,OAAO,OAAO,UAAU,WAAW,cAAc;QACnD;QACA,OAAO,OAAO,UAAU;IAC1B;AACF;AAEA,gBAAgB,OAAO,CAAC,aAAa;IACnC,QAAQ;QAAC;QAAM;KAAK;IACpB,aAAa,SAAU,OAAO;QAC5B,OAAO,QAAQ,IAAI,KAAK;IAC1B;AACF;AAKO,SAAS,eAAe,IAAY;IACzC,IAAI,CAAC,MAAM,OAAO;IAElB,IAAI;QACF,OAAO,gBAAgB,QAAQ,CAAC;IAClC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;IACT;AACF;AAKO,SAAS,eAAe,QAAgB;IAC7C,IAAI,CAAC,UAAU,OAAO;IAEtB,IAAI;QACF,IAAI,OAAO;QAEX,KAAK;QACL,OAAO,KAAK,OAAO,CAAC,iBAAiB;QACrC,OAAO,KAAK,OAAO,CAAC,gBAAgB;QACpC,OAAO,KAAK,OAAO,CAAC,eAAe;QAEnC,QAAQ;QACR,OAAO,KAAK,OAAO,CAAC,kBAAkB;QACtC,OAAO,KAAK,OAAO,CAAC,cAAc;QAElC,KAAK;QACL,OAAO,KAAK,OAAO,CAAC,YAAY;QAEhC,KAAK;QACL,OAAO,KAAK,OAAO,CAAC,4BAA4B;QAEhD,KAAK;QACL,OAAO,KAAK,OAAO,CAAC,kBAAkB;QACtC,OAAO,KAAK,OAAO,CAAC,mBAAmB;QAEvC,OAAO,KAAK,OAAO,CAAC,qBAAqB;QACzC,OAAO,KAAK,OAAO,CAAC,mBAAmB;QAEvC,KAAK;QACL,OAAO,KAAK,OAAO,CAAC,cAAc;QAElC,MAAM;QACN,OAAO,KAAK,OAAO,CAAC,WAAW;QAE/B,KAAK;QACL,OAAO,KAAK,OAAO,CAAC,SAAS;QAC7B,OAAO,QAAQ,OAAO;QAEtB,QAAQ;QACR,OAAO,KAAK,OAAO,CAAC,aAAa;QACjC,OAAO,KAAK,OAAO,CAAC,kBAAkB;QACtC,OAAO,KAAK,OAAO,CAAC,sBAAsB;QAC1C,OAAO,KAAK,OAAO,CAAC,qCAAqC;QACzD,OAAO,KAAK,OAAO,CAAC,6CAA6C;QAEjE,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;IACT;AACF;AAKO,SAAS,oBAAoB,OAAe;IACjD,IAAI,CAAC,SAAS,OAAO;IAErB,eAAe;IACf,MAAM,eAAe;IACrB,MAAM,cAAc,aAAa,IAAI,CAAC;IAEtC,cAAc;IACd,MAAM,iBAAiB;IAEvB,IAAI,eAAe,eAAe,IAAI,CAAC,UAAU;QAC/C,OAAO;IACT;IAEA,eAAe;IACf,MAAM,mBAAmB;QACvB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,IAAI,iBAAiB,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC,WAAW;QAC3D,OAAO;IACT;IAEA,OAAO;AACT;AAKO,SAAS,eAAe,OAAe,EAAE,YAAiC;IAC/E,MAAM,gBAAgB,oBAAoB;IAE1C,IAAI,kBAAkB,cAAc;QAClC,OAAO;IACT;IAEA,IAAI,kBAAkB,UAAU,iBAAiB,YAAY;QAC3D,OAAO,eAAe;IACxB;IAEA,IAAI,kBAAkB,cAAc,iBAAiB,QAAQ;QAC3D,OAAO,eAAe;IACxB;IAEA,QAAQ;IACR,IAAI,kBAAkB,SAAS;QAC7B,IAAI,iBAAiB,QAAQ;YAC3B,OAAO,QAAQ,QAAQ,OAAO,CAAC,SAAS,WAAW,OAAO,CAAC,OAAO,UAAU;QAC9E;QACA,OAAO;IACT;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 373, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/ui/markdown.tsx"], "sourcesContent": ["import React from 'react'\nimport ReactMarkdown from 'react-markdown'\nimport remarkGfm from 'remark-gfm'\nimport rehypeRaw from 'rehype-raw'\nimport { cn } from '@/lib/utils'\nimport { detectContentFormat } from '@/lib/content-converter'\n\ninterface MarkdownProps {\n  content: string\n  className?: string\n  variant?: 'default' | 'compact' | 'product'\n}\n\nexport function Markdown({ content, className, variant = 'default' }: MarkdownProps) {\n  const baseClasses = variant === 'compact'\n    ? 'text-sm text-gray-600 line-clamp-3'\n    : variant === 'product'\n    ? 'text-gray-800 leading-relaxed'\n    : 'text-gray-700 leading-relaxed'\n\n  // 检测内容格式\n  const contentFormat = detectContentFormat(content)\n\n  // 如果是HTML内容，直接渲染\n  if (contentFormat === 'html') {\n    return (\n      <div\n        className={cn('markdown-content prose prose-sm max-w-none', baseClasses, className)}\n        dangerouslySetInnerHTML={{ __html: content }}\n      />\n    )\n  }\n\n  return (\n    <div className={cn('markdown-content', baseClasses, className)}>\n      <ReactMarkdown\n        remarkPlugins={[remarkGfm]}\n        rehypePlugins={[rehypeRaw]}\n        components={{\n          // 标题样式 - 针对产品页面优化\n          h1: ({ children }) => (\n            <h1 className={cn(\n              \"font-bold mb-4 mt-6 first:mt-0 border-b border-gray-200 pb-2\",\n              variant === 'product'\n                ? \"text-2xl text-gray-900\"\n                : \"text-2xl text-gray-900\"\n            )}>\n              {children}\n            </h1>\n          ),\n          h2: ({ children }) => (\n            <h2 className={cn(\n              \"font-semibold mb-3 mt-5 first:mt-0\",\n              variant === 'product'\n                ? \"text-xl text-gray-900 border-l-4 border-blue-500 pl-3\"\n                : \"text-xl text-gray-900\"\n            )}>\n              {children}\n            </h2>\n          ),\n          h3: ({ children }) => (\n            <h3 className={cn(\n              \"font-medium mb-2 mt-4 first:mt-0\",\n              variant === 'product'\n                ? \"text-lg text-gray-900 border-l-2 border-gray-300 pl-2\"\n                : \"text-lg text-gray-900\"\n            )}>\n              {children}\n            </h3>\n          ),\n          h4: ({ children }) => (\n            <h4 className=\"text-base font-medium text-gray-900 mb-2 mt-3 first:mt-0\">\n              {children}\n            </h4>\n          ),\n          h5: ({ children }) => (\n            <h5 className=\"text-sm font-medium text-gray-900 mb-2 mt-3 first:mt-0\">\n              {children}\n            </h5>\n          ),\n          h6: ({ children }) => (\n            <h6 className=\"text-sm font-medium text-gray-700 mb-2 mt-3 first:mt-0\">\n              {children}\n            </h6>\n          ),\n\n          // 段落样式\n          p: ({ children }) => (\n            <p className={cn(\n              \"mb-4 last:mb-0\",\n              variant === 'product' ? \"text-base leading-7\" : \"leading-relaxed\"\n            )}>\n              {children}\n            </p>\n          ),\n\n          // 列表样式 - 产品页面优化\n          ul: ({ children }) => (\n            <ul className={cn(\n              \"mb-4 space-y-2\",\n              variant === 'product'\n                ? \"list-none pl-0\"\n                : \"list-disc list-inside\"\n            )}>\n              {children}\n            </ul>\n          ),\n          ol: ({ children }) => (\n            <ol className={cn(\n              \"mb-4 space-y-2\",\n              variant === 'product'\n                ? \"list-none pl-0\"\n                : \"list-decimal list-inside\"\n            )}>\n              {children}\n            </ol>\n          ),\n          li: ({ children }) => (\n            <li className={cn(\n              variant === 'product'\n                ? \"text-gray-700 flex items-start before:content-['•'] before:text-blue-500 before:font-bold before:mr-3 before:mt-1\"\n                : \"text-gray-700\"\n            )}>\n              {variant === 'product' ? <span>{children}</span> : children}\n            </li>\n          ),\n\n          // 链接样式\n          a: ({ href, children }) => (\n            <a\n              href={href}\n              className={cn(\n                \"underline transition-colors\",\n                variant === 'product'\n                  ? \"text-blue-600 hover:text-blue-800 font-medium\"\n                  : \"text-blue-600 hover:text-blue-800\"\n              )}\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n            >\n              {children}\n            </a>\n          ),\n\n          // 强调样式\n          strong: ({ children }) => (\n            <strong className={cn(\n              \"font-semibold\",\n              variant === 'product' ? \"text-gray-900\" : \"text-gray-900\"\n            )}>\n              {children}\n            </strong>\n          ),\n          em: ({ children }) => (\n            <em className=\"italic\">\n              {children}\n            </em>\n          ),\n\n          // 代码样式 - 产品页面优化\n          code: ({ children, className }) => {\n            const isInline = !className\n            if (isInline) {\n              return (\n                <code className={cn(\n                  \"px-2 py-1 rounded text-sm font-mono\",\n                  variant === 'product'\n                    ? \"bg-blue-50 text-blue-800 border border-blue-200\"\n                    : \"bg-gray-100 text-gray-800\"\n                )}>\n                  {children}\n                </code>\n              )\n            }\n            return (\n              <code className={cn(\n                \"block p-3 rounded text-sm font-mono overflow-x-auto\",\n                variant === 'product'\n                  ? \"bg-gray-900 text-gray-100 border border-gray-700\"\n                  : \"bg-gray-100 text-gray-800\"\n              )}>\n                {children}\n              </code>\n            )\n          },\n\n          // 引用样式 - 产品页面优化\n          blockquote: ({ children }) => (\n            <blockquote className={cn(\n              \"pl-4 py-3 mb-4 italic border-l-4\",\n              variant === 'product'\n                ? \"border-blue-500 bg-blue-50 text-blue-900\"\n                : \"border-gray-300 bg-gray-50 text-gray-600\"\n            )}>\n              {children}\n            </blockquote>\n          ),\n\n          // 分隔线样式\n          hr: () => (\n            <hr className={cn(\n              \"my-6\",\n              variant === 'product' ? \"border-gray-200\" : \"border-gray-300\"\n            )} />\n          ),\n\n          // 表格样式 - 产品页面优化\n          table: ({ children }) => (\n            <div className={cn(\n              \"overflow-x-auto mb-4 rounded-lg\",\n              variant === 'product' ? \"border border-gray-200 shadow-sm\" : \"\"\n            )}>\n              <table className={cn(\n                \"min-w-full\",\n                variant === 'product'\n                  ? \"border-collapse\"\n                  : \"border border-gray-300\"\n              )}>\n                {children}\n              </table>\n            </div>\n          ),\n          thead: ({ children }) => (\n            <thead className={cn(\n              variant === 'product'\n                ? \"bg-gradient-to-r from-blue-50 to-indigo-50\"\n                : \"bg-gray-50\"\n            )}>\n              {children}\n            </thead>\n          ),\n          tbody: ({ children }) => (\n            <tbody className=\"bg-white\">\n              {children}\n            </tbody>\n          ),\n          tr: ({ children }) => (\n            <tr className={cn(\n              \"border-b\",\n              variant === 'product'\n                ? \"border-gray-100 hover:bg-gray-50 transition-colors\"\n                : \"border-gray-200\"\n            )}>\n              {children}\n            </tr>\n          ),\n          th: ({ children }) => (\n            <th className={cn(\n              \"px-4 py-3 text-left font-semibold border-r last:border-r-0\",\n              variant === 'product'\n                ? \"text-gray-900 border-gray-200\"\n                : \"text-gray-900 border-gray-200\"\n            )}>\n              {children}\n            </th>\n          ),\n          td: ({ children }) => (\n            <td className={cn(\n              \"px-4 py-3 border-r last:border-r-0\",\n              variant === 'product'\n                ? \"text-gray-700 border-gray-100\"\n                : \"text-gray-700 border-gray-200\"\n            )}>\n              {children}\n            </td>\n          ),\n        }}\n      >\n        {content}\n      </ReactMarkdown>\n    </div>\n  )\n}\n\n// 用于商品卡片的紧凑版本\nexport function MarkdownPreview({ content, maxLength = 100 }: { content: string; maxLength?: number }) {\n  // 移除Markdown语法，只保留纯文本用于预览\n  const plainText = content\n    .replace(/#{1,6}\\s+/g, '') // 移除标题标记\n    .replace(/\\*\\*(.*?)\\*\\*/g, '$1') // 移除粗体标记\n    .replace(/\\*(.*?)\\*/g, '$1') // 移除斜体标记\n    .replace(/`(.*?)`/g, '$1') // 移除代码标记\n    .replace(/\\[(.*?)\\]\\(.*?\\)/g, '$1') // 移除链接，保留文本\n    .replace(/>\\s+/g, '') // 移除引用标记\n    .replace(/[-*+]\\s+/g, '') // 移除列表标记\n    .replace(/\\n+/g, ' ') // 将换行替换为空格\n    .trim()\n\n  const truncated = plainText.length > maxLength\n    ? plainText.substring(0, maxLength) + '...'\n    : plainText\n\n  return (\n    <p className=\"text-gray-600 text-sm line-clamp-2\">\n      {truncated}\n    </p>\n  )\n}\n\n// 增强版Markdown组件，支持更多自定义选项\nexport function EnhancedMarkdown({\n  content,\n  className,\n  variant = 'default',\n  showLineNumbers = false,\n  enableCopy = false\n}: {\n  content: string\n  className?: string\n  variant?: 'default' | 'compact' | 'product' | 'admin'\n  showLineNumbers?: boolean\n  enableCopy?: boolean\n}) {\n  const copyToClipboard = () => {\n    navigator.clipboard.writeText(content)\n  }\n\n  return (\n    <div className=\"relative\">\n      {enableCopy && (\n        <button\n          onClick={copyToClipboard}\n          className=\"absolute top-2 right-2 p-2 bg-gray-100 hover:bg-gray-200 rounded-md text-xs text-gray-600 transition-colors z-10\"\n          title=\"复制内容\"\n        >\n          复制\n        </button>\n      )}\n      <Markdown content={content} className={className} variant={variant} />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AACA;AACA;AACA;AACA;AACA;;;;;;;AAQO,SAAS,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,SAAS,EAAiB;IACjF,MAAM,cAAc,YAAY,YAC5B,uCACA,YAAY,YACZ,kCACA;IAEJ,SAAS;IACT,MAAM,gBAAgB,CAAA,GAAA,qIAAA,CAAA,sBAAmB,AAAD,EAAE;IAE1C,iBAAiB;IACjB,IAAI,kBAAkB,QAAQ;QAC5B,qBACE,6LAAC;YACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C,aAAa;YACzE,yBAAyB;gBAAE,QAAQ;YAAQ;;;;;;IAGjD;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB,aAAa;kBAClD,cAAA,6LAAC,2LAAA,CAAA,UAAa;YACZ,eAAe;gBAAC,gJAAA,CAAA,UAAS;aAAC;YAC1B,eAAe;gBAAC,gJAAA,CAAA,UAAS;aAAC;YAC1B,YAAY;gBACV,kBAAkB;gBAClB,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACd,gEACA,YAAY,YACR,2BACA;kCAEH;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACd,sCACA,YAAY,YACR,0DACA;kCAEH;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACd,oCACA,YAAY,YACR,0DACA;kCAEH;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAIL,OAAO;gBACP,GAAG,CAAC,EAAE,QAAQ,EAAE,iBACd,6LAAC;wBAAE,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACb,kBACA,YAAY,YAAY,wBAAwB;kCAE/C;;;;;;gBAIL,gBAAgB;gBAChB,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACd,kBACA,YAAY,YACR,mBACA;kCAEH;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACd,kBACA,YAAY,YACR,mBACA;kCAEH;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACd,YAAY,YACR,sHACA;kCAEH,YAAY,0BAAY,6LAAC;sCAAM;;;;;qCAAmB;;;;;;gBAIvD,OAAO;gBACP,GAAG,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,iBACpB,6LAAC;wBACC,MAAM;wBACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+BACA,YAAY,YACR,kDACA;wBAEN,QAAO;wBACP,KAAI;kCAEH;;;;;;gBAIL,OAAO;gBACP,QAAQ,CAAC,EAAE,QAAQ,EAAE,iBACnB,6LAAC;wBAAO,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAClB,iBACA,YAAY,YAAY,kBAAkB;kCAEzC;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAIL,gBAAgB;gBAChB,MAAM,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;oBAC5B,MAAM,WAAW,CAAC;oBAClB,IAAI,UAAU;wBACZ,qBACE,6LAAC;4BAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,uCACA,YAAY,YACR,oDACA;sCAEH;;;;;;oBAGP;oBACA,qBACE,6LAAC;wBAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,uDACA,YAAY,YACR,qDACA;kCAEH;;;;;;gBAGP;gBAEA,gBAAgB;gBAChB,YAAY,CAAC,EAAE,QAAQ,EAAE,iBACvB,6LAAC;wBAAW,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACtB,oCACA,YAAY,YACR,6CACA;kCAEH;;;;;;gBAIL,QAAQ;gBACR,IAAI,kBACF,6LAAC;wBAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACd,QACA,YAAY,YAAY,oBAAoB;;;;;;gBAIhD,gBAAgB;gBAChB,OAAO,CAAC,EAAE,QAAQ,EAAE,iBAClB,6LAAC;wBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,mCACA,YAAY,YAAY,qCAAqC;kCAE7D,cAAA,6LAAC;4BAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACjB,cACA,YAAY,YACR,oBACA;sCAEH;;;;;;;;;;;gBAIP,OAAO,CAAC,EAAE,QAAQ,EAAE,iBAClB,6LAAC;wBAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACjB,YAAY,YACR,+CACA;kCAEH;;;;;;gBAGL,OAAO,CAAC,EAAE,QAAQ,EAAE,iBAClB,6LAAC;wBAAM,WAAU;kCACd;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACd,YACA,YAAY,YACR,uDACA;kCAEH;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACd,8DACA,YAAY,YACR,kCACA;kCAEH;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACd,sCACA,YAAY,YACR,kCACA;kCAEH;;;;;;YAGP;sBAEC;;;;;;;;;;;AAIT;KAnQgB;AAsQT,SAAS,gBAAgB,EAAE,OAAO,EAAE,YAAY,GAAG,EAA2C;IACnG,0BAA0B;IAC1B,MAAM,YAAY,QACf,OAAO,CAAC,cAAc,IAAI,SAAS;KACnC,OAAO,CAAC,kBAAkB,MAAM,SAAS;KACzC,OAAO,CAAC,cAAc,MAAM,SAAS;KACrC,OAAO,CAAC,YAAY,MAAM,SAAS;KACnC,OAAO,CAAC,qBAAqB,MAAM,YAAY;KAC/C,OAAO,CAAC,SAAS,IAAI,SAAS;KAC9B,OAAO,CAAC,aAAa,IAAI,SAAS;KAClC,OAAO,CAAC,QAAQ,KAAK,WAAW;KAChC,IAAI;IAEP,MAAM,YAAY,UAAU,MAAM,GAAG,YACjC,UAAU,SAAS,CAAC,GAAG,aAAa,QACpC;IAEJ,qBACE,6LAAC;QAAE,WAAU;kBACV;;;;;;AAGP;MAtBgB;AAyBT,SAAS,iBAAiB,EAC/B,OAAO,EACP,SAAS,EACT,UAAU,SAAS,EACnB,kBAAkB,KAAK,EACvB,aAAa,KAAK,EAOnB;IACC,MAAM,kBAAkB;QACtB,UAAU,SAAS,CAAC,SAAS,CAAC;IAChC;IAEA,qBACE,6LAAC;QAAI,WAAU;;YACZ,4BACC,6LAAC;gBACC,SAAS;gBACT,WAAU;gBACV,OAAM;0BACP;;;;;;0BAIH,6LAAC;gBAAS,SAAS;gBAAS,WAAW;gBAAW,SAAS;;;;;;;;;;;;AAGjE;MA/BgB", "debugId": null}}, {"offset": {"line": 713, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/ui/content-renderer.tsx"], "sourcesContent": ["import React from 'react'\nimport { Markdown } from './markdown'\nimport { detectContentFormat } from '@/lib/content-converter'\nimport { cn } from '@/lib/utils'\n\ninterface ContentRendererProps {\n  content: string\n  className?: string\n  variant?: 'default' | 'compact' | 'product'\n}\n\nexport function ContentRenderer({ content, className, variant = 'default' }: ContentRendererProps) {\n  if (!content) {\n    return null\n  }\n\n  const contentFormat = detectContentFormat(content)\n\n  // 检测是否为产品描述页面\n  const isProductPage = className?.includes('product-description') || variant === 'product'\n  const markdownVariant = isProductPage ? 'product' : variant\n\n  // 如果是HTML内容，直接渲染\n  if (contentFormat === 'html') {\n    return (\n      <div\n        className={cn('content-renderer prose prose-sm max-w-none', className)}\n        dangerouslySetInnerHTML={{ __html: content }}\n      />\n    )\n  }\n\n  // 如果是Markdown或纯文本，使用Markdown组件\n  return (\n    <Markdown content={content} className={className} variant={markdownVariant} />\n  )\n}\n\n// 用于商品卡片的简化版本\nexport function ContentPreview({ content, maxLength = 100 }: { content: string; maxLength?: number }) {\n  if (!content) {\n    return null\n  }\n\n  const contentFormat = detectContentFormat(content)\n  \n  // 提取纯文本用于预览\n  let plainText = content\n  \n  if (contentFormat === 'html') {\n    // 移除HTML标签\n    plainText = content\n      .replace(/<[^>]*>/g, '') // 移除所有HTML标签\n      .replace(/&nbsp;/g, ' ') // 替换HTML实体\n      .replace(/&amp;/g, '&')\n      .replace(/&lt;/g, '<')\n      .replace(/&gt;/g, '>')\n      .replace(/&quot;/g, '\"')\n      .replace(/&#39;/g, \"'\")\n      .replace(/\\s+/g, ' ') // 将多个空白字符替换为单个空格\n      .trim()\n  } else if (contentFormat === 'markdown') {\n    // 移除Markdown语法\n    plainText = content\n      .replace(/#{1,6}\\s+/g, '') // 移除标题标记\n      .replace(/\\*\\*(.*?)\\*\\*/g, '$1') // 移除粗体标记\n      .replace(/\\*(.*?)\\*/g, '$1') // 移除斜体标记\n      .replace(/`(.*?)`/g, '$1') // 移除代码标记\n      .replace(/\\[(.*?)\\]\\(.*?\\)/g, '$1') // 移除链接，保留文本\n      .replace(/>\\s+/g, '') // 移除引用标记\n      .replace(/[-*+]\\s+/g, '') // 移除列表标记\n      .replace(/\\d+\\.\\s+/g, '') // 移除有序列表标记\n      .replace(/\\|.*?\\|/g, '') // 移除表格\n      .replace(/\\n+/g, ' ') // 将换行替换为空格\n      .trim()\n  }\n\n  const truncated = plainText.length > maxLength \n    ? plainText.substring(0, maxLength) + '...'\n    : plainText\n\n  return (\n    <p className=\"text-gray-600 text-sm line-clamp-2\">\n      {truncated}\n    </p>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;;;;;AAQO,SAAS,gBAAgB,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,SAAS,EAAwB;IAC/F,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAA,GAAA,qIAAA,CAAA,sBAAmB,AAAD,EAAE;IAE1C,cAAc;IACd,MAAM,gBAAgB,WAAW,SAAS,0BAA0B,YAAY;IAChF,MAAM,kBAAkB,gBAAgB,YAAY;IAEpD,iBAAiB;IACjB,IAAI,kBAAkB,QAAQ;QAC5B,qBACE,6LAAC;YACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;YAC5D,yBAAyB;gBAAE,QAAQ;YAAQ;;;;;;IAGjD;IAEA,+BAA+B;IAC/B,qBACE,6LAAC,uIAAA,CAAA,WAAQ;QAAC,SAAS;QAAS,WAAW;QAAW,SAAS;;;;;;AAE/D;KAzBgB;AA4BT,SAAS,eAAe,EAAE,OAAO,EAAE,YAAY,GAAG,EAA2C;IAClG,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAA,GAAA,qIAAA,CAAA,sBAAmB,AAAD,EAAE;IAE1C,YAAY;IACZ,IAAI,YAAY;IAEhB,IAAI,kBAAkB,QAAQ;QAC5B,WAAW;QACX,YAAY,QACT,OAAO,CAAC,YAAY,IAAI,aAAa;SACrC,OAAO,CAAC,WAAW,KAAK,WAAW;SACnC,OAAO,CAAC,UAAU,KAClB,OAAO,CAAC,SAAS,KACjB,OAAO,CAAC,SAAS,KACjB,OAAO,CAAC,WAAW,KACnB,OAAO,CAAC,UAAU,KAClB,OAAO,CAAC,QAAQ,KAAK,iBAAiB;SACtC,IAAI;IACT,OAAO,IAAI,kBAAkB,YAAY;QACvC,eAAe;QACf,YAAY,QACT,OAAO,CAAC,cAAc,IAAI,SAAS;SACnC,OAAO,CAAC,kBAAkB,MAAM,SAAS;SACzC,OAAO,CAAC,cAAc,MAAM,SAAS;SACrC,OAAO,CAAC,YAAY,MAAM,SAAS;SACnC,OAAO,CAAC,qBAAqB,MAAM,YAAY;SAC/C,OAAO,CAAC,SAAS,IAAI,SAAS;SAC9B,OAAO,CAAC,aAAa,IAAI,SAAS;SAClC,OAAO,CAAC,aAAa,IAAI,WAAW;SACpC,OAAO,CAAC,YAAY,IAAI,OAAO;SAC/B,OAAO,CAAC,QAAQ,KAAK,WAAW;SAChC,IAAI;IACT;IAEA,MAAM,YAAY,UAAU,MAAM,GAAG,YACjC,UAAU,SAAS,CAAC,GAAG,aAAa,QACpC;IAEJ,qBACE,6LAAC;QAAE,WAAU;kBACV;;;;;;AAGP;MA/CgB", "debugId": null}}, {"offset": {"line": 808, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useMemo, useState } from 'react'\nimport { useSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { ContentPreview } from '@/components/ui/content-renderer'\nimport { formatPrice } from '@/lib/utils'\nimport { ShoppingCart, User, LogIn, Package, Search, SortAsc, SortDesc, X } from 'lucide-react'\n\ninterface Product {\n  id: string\n  name: string\n  description: string\n  price: number\n  image: string\n  category: {\n    id: string\n    name: string\n  }\n  _count: {\n    cards: number\n  }\n}\n\ninterface Category {\n  id: string\n  name: string\n  slug: string\n  _count: {\n    products: number\n  }\n}\n\nexport default function Home() {\n  const { data: session } = useSession()\n  const router = useRouter()\n  const [products, setProducts] = useState<Product[]>([])\n  const [categories, setCategories] = useState<Category[]>([])\n  const [selectedCategory, setSelectedCategory] = useState<string>('')\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [sortBy, setSortBy] = useState<'default' | 'price-asc' | 'price-desc'>('default')\n  const [maintenanceCheck, setMaintenanceCheck] = useState(false)\n\n  // 检查维护模式\n  useEffect(() => {\n    const checkMaintenanceMode = async () => {\n      try {\n        const response = await fetch('/api/admin/settings/maintenance')\n        if (response.ok) {\n          const data = await response.json()\n          if (data.maintenanceMode) {\n            // 检查用户是否是管理员\n            const isAdmin = session?.user?.role === 'ADMIN'\n            if (!isAdmin) {\n              router.push('/maintenance')\n              return\n            }\n          }\n        }\n      } catch (error) {\n        console.error('检查维护模式失败:', error)\n      }\n      setMaintenanceCheck(true)\n    }\n\n    checkMaintenanceMode()\n  }, [session, router])\n\n  useEffect(() => {\n    if (maintenanceCheck) {\n      fetchCategories()\n      fetchProducts()\n    }\n  }, [maintenanceCheck])\n\n  const fetchCategories = async () => {\n    try {\n      const response = await fetch('/api/categories')\n      const data = await response.json()\n      setCategories(data)\n    } catch (error) {\n      console.error('获取分类失败:', error)\n    }\n  }\n\n  const fetchProducts = async (categoryId?: string) => {\n    try {\n      const url = categoryId\n        ? `/api/products?categoryId=${categoryId}`\n        : '/api/products'\n      const response = await fetch(url)\n      const data = await response.json()\n      setProducts(data)\n    } catch (error) {\n      console.error('获取商品失败:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleCategoryChange = (categoryId: string) => {\n    setSelectedCategory(categoryId)\n    setLoading(true)\n    fetchProducts(categoryId || undefined)\n  }\n\n  const handleBuyNow = (productId: string) => {\n    router.push(`/checkout?productId=${productId}&quantity=1`)\n  }\n\n  // 过滤和排序商品\n  const filteredAndSortedProducts = useMemo(() => {\n    let filtered = products\n\n    // 按分类过滤\n    if (selectedCategory) {\n      filtered = filtered.filter((product: Product) => product.category.id === selectedCategory)\n    }\n\n    // 按搜索词过滤\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase()\n      filtered = filtered.filter(product =>\n        product.name.toLowerCase().includes(searchLower) ||\n        product.category.name.toLowerCase().includes(searchLower)\n      )\n    }\n\n    // 排序\n    if (sortBy === 'price-asc') {\n      filtered = [...filtered].sort((a, b) => a.price - b.price)\n    } else if (sortBy === 'price-desc') {\n      filtered = [...filtered].sort((a, b) => b.price - a.price)\n    }\n\n    return filtered\n  }, [products, selectedCategory, searchTerm, sortBy])\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* 导航栏 */}\n      <nav className=\"bg-white border-b border-gray-200 sticky top-0 z-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"text-xl font-bold text-gray-900\">\n                自动发卡网站\n              </Link>\n            </div>\n\n            <div className=\"flex items-center space-x-4\">\n              <Link href=\"/orders\" className=\"text-gray-700 hover:text-gray-900 transition-colors\">\n                  订单查询\n              </Link>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* 主内容 */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* 简约标题 */}\n        <div className=\"mb-12 text-center\">\n          <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-4\">\n            自动发卡商城\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            安全、快速、便捷的数字商品购买体验\n          </p>\n        </div>\n\n        {/* 搜索和筛选 */}\n        <div className=\"mb-8 space-y-4\">\n          {/* 搜索框和排序 */}\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            {/* 搜索框 */}\n            <div className=\"flex-1 relative\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <Search className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"搜索商品名称或分类...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n              />\n              {searchTerm && (\n                <button\n                  onClick={() => setSearchTerm('')}\n                  className=\"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600\"\n                >\n                  <X className=\"h-4 w-4\" />\n                </button>\n              )}\n            </div>\n\n            {/* 排序选择 */}\n            <div className=\"flex items-center space-x-2 min-w-0\">\n              <span className=\"text-sm text-gray-700 whitespace-nowrap\">排序:</span>\n              <select\n                value={sortBy}\n                onChange={(e) => setSortBy(e.target.value as 'default' | 'price-asc' | 'price-desc')}\n                className=\"block w-full sm:w-auto min-w-[140px] px-3 py-2 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm\"\n              >\n                <option value=\"default\">默认排序</option>\n                <option value=\"price-asc\">价格 ↑</option>\n                <option value=\"price-desc\">价格 ↓</option>\n              </select>\n            </div>\n          </div>\n\n          {/* 分类筛选 */}\n          <div>\n            <div className=\"flex flex-wrap gap-2\">\n              <Button\n                variant={selectedCategory === '' ? 'default' : 'outline'}\n                onClick={() => handleCategoryChange('')}\n                size=\"sm\"\n              >\n                全部商品\n              </Button>\n              {categories.map((category) => (\n                <Button\n                  key={category.id}\n                  variant={selectedCategory === category.id ? 'default' : 'outline'}\n                  onClick={() => handleCategoryChange(category.id)}\n                  size=\"sm\"\n                >\n                  {category.name} ({category._count.products})\n                </Button>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* 搜索结果统计 */}\n        {!loading && (searchTerm || selectedCategory || sortBy !== 'default') && (\n          <div className=\"mb-6 flex items-center justify-between\">\n            <div className=\"text-sm text-gray-600\">\n              {searchTerm && (\n                <span>搜索 \"{searchTerm}\" </span>\n              )}\n              {selectedCategory && (\n                <span>在 \"{categories.find(c => c.id === selectedCategory)?.name}\" 分类中 </span>\n              )}\n              找到 <span className=\"font-medium text-gray-900\">{filteredAndSortedProducts.length}</span> 个商品\n            </div>\n            {(searchTerm || selectedCategory) && (\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => {\n                  setSearchTerm('')\n                  setSelectedCategory('')\n                  setSortBy('default')\n                }}\n                className=\"text-gray-500 hover:text-gray-700\"\n              >\n                清除筛选\n              </Button>\n            )}\n          </div>\n        )}\n\n        {/* 商品列表 */}\n        {!maintenanceCheck || loading ? (\n          <div className=\"text-center py-16\">\n            <div className=\"text-gray-500\">{!maintenanceCheck ? '正在检查系统状态...' : '加载中...'}</div>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {filteredAndSortedProducts.map((product) => (\n              <Card key={product.id} className=\"overflow-hidden hover-simple\">\n                <Link href={`/product/${product.id}`}>\n                  <div className=\"relative\">\n                    {product.image ? (\n                      <img\n                        src={product.image}\n                        alt={product.name}\n                        className=\"w-full h-48 object-cover\"\n                      />\n                    ) : (\n                      <div className=\"w-full h-48 bg-gray-100 flex items-center justify-center\">\n                        <Package className=\"w-12 h-12 text-gray-400\" />\n                      </div>\n                    )}\n                    {product._count.cards === 0 && (\n                      <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center\">\n                        <span className=\"text-white text-sm font-medium\">缺货</span>\n                      </div>\n                    )}\n                  </div>\n                </Link>\n\n                <CardContent className=\"p-4\">\n                  <div className=\"text-sm text-gray-500 mb-1\">{product.category.name}</div>\n                  <Link href={`/product/${product.id}`}>\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2 hover:text-gray-700 transition-colors\">\n                      {product.name}\n                    </h3>\n                  </Link>\n                  {product.description && (\n                    <div className=\"mb-3\">\n                      <ContentPreview content={product.description} maxLength={120} />\n                    </div>\n                  )}\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"text-xl font-bold text-gray-900\">\n                      {formatPrice(product.price)}\n                    </div>\n                    <div className=\"text-sm text-gray-500\">\n                      库存: {product._count.cards}\n                    </div>\n                  </div>\n                  <div className=\"flex space-x-2\">\n                    <Link href={`/product/${product.id}`} className=\"flex-1\">\n                      <Button variant=\"outline\" className=\"w-full\">\n                        查看详情\n                      </Button>\n                    </Link>\n                    <Button\n                      className=\"flex-1\"\n                      disabled={product._count.cards === 0}\n                      onClick={() => handleBuyNow(product.id)}\n                    >\n                      <ShoppingCart className=\"w-4 h-4 mr-2\" />\n                      {product._count.cards === 0 ? '缺货' : '购买'}\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        )}\n\n        {filteredAndSortedProducts.length === 0 && !loading && (\n          <div className=\"text-center py-16\">\n            <Package className=\"w-16 h-16 text-gray-300 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold text-gray-600 mb-2\">\n              {searchTerm || selectedCategory ? '未找到匹配的商品' : '暂无商品'}\n            </h3>\n            <p className=\"text-gray-500\">\n              {searchTerm || selectedCategory\n                ? '请尝试调整搜索条件或选择其他分类'\n                : '当前没有可用的商品，请稍后再来查看'\n              }\n            </p>\n            {(searchTerm || selectedCategory) && (\n              <div className=\"mt-4\">\n                <Button\n                  variant=\"outline\"\n                  onClick={() => {\n                    setSearchTerm('')\n                    setSelectedCategory('')\n                    setSortBy('default')\n                  }}\n                >\n                  清除筛选条件\n                </Button>\n              </div>\n            )}\n          </div>\n        )}\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AAVA;;;;;;;;;;AAoCe,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0C;IAC7E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM;uDAAuB;oBAC3B,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM;wBAC7B,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,OAAO,MAAM,SAAS,IAAI;4BAChC,IAAI,KAAK,eAAe,EAAE;gCACxB,aAAa;gCACb,MAAM,UAAU,SAAS,MAAM,SAAS;gCACxC,IAAI,CAAC,SAAS;oCACZ,OAAO,IAAI,CAAC;oCACZ;gCACF;4BACF;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;oBAC7B;oBACA,oBAAoB;gBACtB;;YAEA;QACF;yBAAG;QAAC;QAAS;KAAO;IAEpB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,kBAAkB;gBACpB;gBACA;YACF;QACF;yBAAG;QAAC;KAAiB;IAErB,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,MAAM,aACR,CAAC,yBAAyB,EAAE,YAAY,GACxC;YACJ,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;QACpB,WAAW;QACX,cAAc,cAAc;IAC9B;IAEA,MAAM,eAAe,CAAC;QACpB,OAAO,IAAI,CAAC,CAAC,oBAAoB,EAAE,UAAU,WAAW,CAAC;IAC3D;IAEA,UAAU;IACV,MAAM,4BAA4B,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mDAAE;YACxC,IAAI,WAAW;YAEf,QAAQ;YACR,IAAI,kBAAkB;gBACpB,WAAW,SAAS,MAAM;+DAAC,CAAC,UAAqB,QAAQ,QAAQ,CAAC,EAAE,KAAK;;YAC3E;YAEA,SAAS;YACT,IAAI,YAAY;gBACd,MAAM,cAAc,WAAW,WAAW;gBAC1C,WAAW,SAAS,MAAM;+DAAC,CAAA,UACzB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACpC,QAAQ,QAAQ,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;;YAEjD;YAEA,KAAK;YACL,IAAI,WAAW,aAAa;gBAC1B,WAAW;uBAAI;iBAAS,CAAC,IAAI;+DAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;;YAC3D,OAAO,IAAI,WAAW,cAAc;gBAClC,WAAW;uBAAI;iBAAS,CAAC,IAAI;+DAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;;YAC3D;YAEA,OAAO;QACT;kDAAG;QAAC;QAAU;QAAkB;QAAY;KAAO;IAEnD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAkC;;;;;;;;;;;0CAK7D,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAU,WAAU;8CAAsD;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7F,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAMzD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;4CAEX,4BACC,6LAAC;gDACC,SAAS,IAAM,cAAc;gDAC7B,WAAU;0DAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAMnB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA0C;;;;;;0DAC1D,6LAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gDACzC,WAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAU;;;;;;kEACxB,6LAAC;wDAAO,OAAM;kEAAY;;;;;;kEAC1B,6LAAC;wDAAO,OAAM;kEAAa;;;;;;;;;;;;;;;;;;;;;;;;0CAMjC,6LAAC;0CACC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,qBAAqB,KAAK,YAAY;4CAC/C,SAAS,IAAM,qBAAqB;4CACpC,MAAK;sDACN;;;;;;wCAGA,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC,qIAAA,CAAA,SAAM;gDAEL,SAAS,qBAAqB,SAAS,EAAE,GAAG,YAAY;gDACxD,SAAS,IAAM,qBAAqB,SAAS,EAAE;gDAC/C,MAAK;;oDAEJ,SAAS,IAAI;oDAAC;oDAAG,SAAS,MAAM,CAAC,QAAQ;oDAAC;;+CALtC,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;oBAazB,CAAC,WAAW,CAAC,cAAc,oBAAoB,WAAW,SAAS,mBAClE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCACZ,4BACC,6LAAC;;4CAAK;4CAAK;4CAAW;;;;;;;oCAEvB,kCACC,6LAAC;;4CAAK;4CAAI,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,mBAAmB;4CAAK;;;;;;;oCAChE;kDACC,6LAAC;wCAAK,WAAU;kDAA6B,0BAA0B,MAAM;;;;;;oCAAQ;;;;;;;4BAEzF,CAAC,cAAc,gBAAgB,mBAC9B,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;oCACP,cAAc;oCACd,oBAAoB;oCACpB,UAAU;gCACZ;gCACA,WAAU;0CACX;;;;;;;;;;;;oBAQN,CAAC,oBAAoB,wBACpB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCAAiB,CAAC,mBAAmB,gBAAgB;;;;;;;;;;6CAGtE,6LAAC;wBAAI,WAAU;kCACZ,0BAA0B,GAAG,CAAC,CAAC,wBAC9B,6LAAC,mIAAA,CAAA,OAAI;gCAAkB,WAAU;;kDAC/B,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE;kDAClC,cAAA,6LAAC;4CAAI,WAAU;;gDACZ,QAAQ,KAAK,iBACZ,6LAAC;oDACC,KAAK,QAAQ,KAAK;oDAClB,KAAK,QAAQ,IAAI;oDACjB,WAAU;;;;;yEAGZ,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;gDAGtB,QAAQ,MAAM,CAAC,KAAK,KAAK,mBACxB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAiC;;;;;;;;;;;;;;;;;;;;;;kDAMzD,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;gDAAI,WAAU;0DAA8B,QAAQ,QAAQ,CAAC,IAAI;;;;;;0DAClE,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE;0DAClC,cAAA,6LAAC;oDAAG,WAAU;8DACX,QAAQ,IAAI;;;;;;;;;;;4CAGhB,QAAQ,WAAW,kBAClB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,kJAAA,CAAA,iBAAc;oDAAC,SAAS,QAAQ,WAAW;oDAAE,WAAW;;;;;;;;;;;0DAG7D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,KAAK;;;;;;kEAE5B,6LAAC;wDAAI,WAAU;;4DAAwB;4DAChC,QAAQ,MAAM,CAAC,KAAK;;;;;;;;;;;;;0DAG7B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE;wDAAE,WAAU;kEAC9C,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,WAAU;sEAAS;;;;;;;;;;;kEAI/C,6LAAC,qIAAA,CAAA,SAAM;wDACL,WAAU;wDACV,UAAU,QAAQ,MAAM,CAAC,KAAK,KAAK;wDACnC,SAAS,IAAM,aAAa,QAAQ,EAAE;;0EAEtC,6LAAC,yNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;4DACvB,QAAQ,MAAM,CAAC,KAAK,KAAK,IAAI,OAAO;;;;;;;;;;;;;;;;;;;;+BAtDlC,QAAQ,EAAE;;;;;;;;;;oBA+D1B,0BAA0B,MAAM,KAAK,KAAK,CAAC,yBAC1C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,2MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6LAAC;gCAAG,WAAU;0CACX,cAAc,mBAAmB,aAAa;;;;;;0CAEjD,6LAAC;gCAAE,WAAU;0CACV,cAAc,mBACX,qBACA;;;;;;4BAGL,CAAC,cAAc,gBAAgB,mBAC9B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;wCACP,cAAc;wCACd,oBAAoB;wCACpB,UAAU;oCACZ;8CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GA/UwB;;QACI,iJAAA,CAAA,aAAU;QACrB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}