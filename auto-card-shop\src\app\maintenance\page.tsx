import { prisma } from '@/lib/prisma'
import { Settings, Clock, Mail, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export default async function MaintenancePage() {
  // 获取维护设置
  let settings = {
    maintenanceMode: true,
    maintenanceMessage: '网站正在维护中，请稍后再试。',
    updatedAt: new Date().toISOString()
  }

  try {
    const setting = await prisma.setting.findUnique({
      where: { key: 'maintenance' }
    })

    if (setting && setting.value) {
      settings = JSON.parse(setting.value)
    }
  } catch (error) {
    console.error('获取维护设置失败:', error)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        {/* 维护图标 */}
        <div className="mb-6">
          <div className="mx-auto w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center">
            <Settings className="w-8 h-8 text-orange-600 animate-spin" style={{ animationDuration: '3s' }} />
          </div>
        </div>

        {/* 标题 */}
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          网站维护中
        </h1>

        {/* 维护消息 */}
        <div className="mb-6">
          <p className="text-gray-600 leading-relaxed">
            {settings.maintenanceMessage}
          </p>
        </div>

        {/* 维护信息 */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-center text-sm text-gray-500 mb-2">
            <Clock className="w-4 h-4 mr-2" />
            维护开始时间
          </div>
          <div className="text-sm font-medium text-gray-700">
            {new Date(settings.updatedAt).toLocaleString('zh-CN')}
          </div>
        </div>

        {/* 联系信息 */}
        <div className="border-t border-gray-200 pt-6">
          <p className="text-sm text-gray-500 mb-4">
            如有紧急问题，请联系我们：
          </p>
          <div className="flex items-center justify-center text-sm text-blue-600">
            <Mail className="w-4 h-4 mr-2" />
            <a href="mailto:<EMAIL>" className="hover:underline">
              <EMAIL>
            </a>
          </div>
        </div>

        {/* 管理员登录链接 */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <Link 
            href="/auth/signin"
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-1" />
            管理员登录
          </Link>
        </div>

        {/* 装饰性元素 */}
        <div className="mt-8">
          <div className="flex justify-center space-x-2">
            <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse"></div>
            <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
            <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
          </div>
        </div>
      </div>

      {/* 背景装饰 */}
      <div className="fixed inset-0 -z-10 overflow-hidden">
        <div className="absolute -top-40 -right-32 w-80 h-80 bg-blue-200 rounded-full opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-32 w-80 h-80 bg-indigo-200 rounded-full opacity-20 animate-pulse" style={{ animationDelay: '1s' }}></div>
      </div>
    </div>
  )
}
