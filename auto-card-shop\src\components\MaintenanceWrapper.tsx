import { redirect } from 'next/navigation'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

interface MaintenanceSettings {
  maintenanceMode: boolean
  message: string
  estimatedTime: string
}

async function getMaintenanceSettings(): Promise<MaintenanceSettings> {
  try {
    const setting = await prisma.setting.findUnique({
      where: { key: 'maintenance' }
    })

    if (setting) {
      return JSON.parse(setting.value)
    }
  } catch (error) {
    console.error('获取维护设置失败:', error)
  }

  return {
    maintenanceMode: false,
    message: '网站正在维护中，请稍后再试。',
    estimatedTime: '预计维护时间：30分钟'
  }
}

interface MaintenanceWrapperProps {
  children: React.ReactNode
  pathname: string
}

export default async function MaintenanceWrapper({ children, pathname }: MaintenanceWrapperProps) {
  // 跳过管理员路由、API路由、认证路由和维护页面
  const skipPaths = ['/admin', '/api', '/auth', '/maintenance', '/_next', '/favicon.ico']
  const shouldSkip = skipPaths.some(path => pathname.startsWith(path))
  
  if (shouldSkip) {
    return <>{children}</>
  }

  // 检查维护模式
  const maintenanceSettings = await getMaintenanceSettings()
  
  if (maintenanceSettings.maintenanceMode) {
    // 检查用户是否是管理员
    const session = await getServerSession(authOptions)
    const isAdmin = session?.user?.role === 'ADMIN'
    
    if (!isAdmin) {
      // 重定向到维护页面
      redirect('/maintenance')
    }
  }

  return <>{children}</>
}
