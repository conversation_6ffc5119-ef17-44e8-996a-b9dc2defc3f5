{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_2ebd57a8._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_2c088799.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/admin(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/admin/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "W1guVrC+Px7EIJSfR0SnSL5iOnRJMCzuf2lPvgcuNgA=", "__NEXT_PREVIEW_MODE_ID": "5e48bb9a3d710db56e694a34bf3fbcbc", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2d8f7dbbdebb27751f5b931b9a3e5eb7147f5f72b61c2c7e9920da6033271a4c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e33da3e8c6c39357a58453068d84474aac99fdfbd5753e20589ad886ed3533fc"}}}, "sortedMiddleware": ["/"], "functions": {}}