{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport CredentialsProvider from 'next-auth/providers/credentials'\nimport { PrismaAdapter } from '@auth/prisma-adapter'\nimport { prisma } from './prisma'\nimport bcrypt from 'bcryptjs'\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma) as any,\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          username: user.username,\n          role: user.role,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n        token.username = user.username\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n        session.user.username = token.username as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signUp: '/auth/signup'\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,UAAU,KAAK,QAAQ;oBACvB,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/app/api/orders/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\n\n// 获取订单列表\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    const { searchParams } = new URL(request.url)\n    const email = searchParams.get('email')\n    const orderId = searchParams.get('orderId')\n    const status = searchParams.get('status')\n    const page = parseInt(searchParams.get('page') || '1')\n    const pageSize = parseInt(searchParams.get('pageSize') || '10')\n\n    let where: any = {}\n\n    // 如果是管理员，可以查看所有订单\n    if (session?.user.role === 'ADMIN') {\n      if (email) {\n        where.email = email\n      }\n      if (orderId) {\n        where.id = orderId\n      }\n      if (status) {\n        where.status = status\n      }\n    } else {\n      // 普通用户只能查看自己的订单\n      if (!email) {\n        return NextResponse.json(\n          { error: '邮箱地址是必需的' },\n          { status: 400 }\n        )\n      }\n      where.email = email\n\n      if (orderId) {\n        where.id = orderId\n      }\n      if (status) {\n        where.status = status\n      }\n    }\n\n    // 计算分页参数\n    const skip = (page - 1) * pageSize\n    const take = pageSize\n\n    // 获取总数\n    const total = await prisma.order.count({ where })\n\n    // 获取分页数据\n    const orders = await prisma.order.findMany({\n      where,\n      include: {\n        orderItems: {\n          include: {\n            product: {\n              select: {\n                name: true,\n                image: true\n              }\n            }\n          }\n        },\n        user: session?.user.role === 'ADMIN' ? {\n          select: {\n            username: true,\n            email: true\n          }\n        } : false\n      },\n      orderBy: {\n        createdAt: 'desc'\n      },\n      skip,\n      take\n    })\n\n    return NextResponse.json({\n      orders,\n      pagination: {\n        page,\n        pageSize,\n        total,\n        totalPages: Math.ceil(total / pageSize)\n      }\n    })\n  } catch (error) {\n    console.error('获取订单错误:', error)\n    return NextResponse.json(\n      { error: '服务器错误' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,QAAQ,aAAa,GAAG,CAAC;QAC/B,MAAM,UAAU,aAAa,GAAG,CAAC;QACjC,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,WAAW,SAAS,aAAa,GAAG,CAAC,eAAe;QAE1D,IAAI,QAAa,CAAC;QAElB,kBAAkB;QAClB,IAAI,SAAS,KAAK,SAAS,SAAS;YAClC,IAAI,OAAO;gBACT,MAAM,KAAK,GAAG;YAChB;YACA,IAAI,SAAS;gBACX,MAAM,EAAE,GAAG;YACb;YACA,IAAI,QAAQ;gBACV,MAAM,MAAM,GAAG;YACjB;QACF,OAAO;YACL,gBAAgB;YAChB,IAAI,CAAC,OAAO;gBACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAW,GACpB;oBAAE,QAAQ;gBAAI;YAElB;YACA,MAAM,KAAK,GAAG;YAEd,IAAI,SAAS;gBACX,MAAM,EAAE,GAAG;YACb;YACA,IAAI,QAAQ;gBACV,MAAM,MAAM,GAAG;YACjB;QACF;QAEA,SAAS;QACT,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAC1B,MAAM,OAAO;QAEb,OAAO;QACP,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,KAAK,CAAC;YAAE;QAAM;QAE/C,SAAS;QACT,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;YACzC;YACA,SAAS;gBACP,YAAY;oBACV,SAAS;wBACP,SAAS;4BACP,QAAQ;gCACN,MAAM;gCACN,OAAO;4BACT;wBACF;oBACF;gBACF;gBACA,MAAM,SAAS,KAAK,SAAS,UAAU;oBACrC,QAAQ;wBACN,UAAU;wBACV,OAAO;oBACT;gBACF,IAAI;YACN;YACA,SAAS;gBACP,WAAW;YACb;YACA;YACA;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA,YAAY;gBACV;gBACA;gBACA;gBACA,YAAY,KAAK,IAAI,CAAC,QAAQ;YAChC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAQ,GACjB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}