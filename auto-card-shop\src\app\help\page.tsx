'use client'

import { useState } from 'react'
import Link from 'next/link'
import { 
  ChevronDown, 
  ChevronRight, 
  Home, 
  ShoppingCart, 
  CreditCard, 
  Search,
  Mail,
  Phone,
  MessageCircle
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

interface FAQItem {
  id: string
  question: string
  answer: string
  category: string
}

const faqData: FAQItem[] = [
  {
    id: '1',
    question: '如何购买商品？',
    answer: '1. 浏览商品页面，选择您需要的商品\n2. 点击"立即购买"按钮\n3. 填写邮箱地址用于接收卡密\n4. 选择支付方式并完成支付\n5. 支付成功后，系统会自动发送卡密到您的邮箱',
    category: '购买流程'
  },
  {
    id: '2',
    question: '支持哪些支付方式？',
    answer: '我们支持以下支付方式：\n• 信用卡/借记卡（Visa、MasterCard、American Express）\n• 支付宝\n• 微信支付\n所有支付都通过安全的 Stripe 支付网关处理，确保您的资金安全。',
    category: '支付相关'
  },
  {
    id: '3',
    question: '如何查看我的订单？',
    answer: '您可以通过以下方式查看订单：\n1. 点击页面顶部的"订单查询"链接\n2. 输入您的邮箱地址或订单号\n3. 点击"查询订单"按钮\n4. 系统会显示您的所有订单信息',
    category: '订单管理'
  },
  {
    id: '4',
    question: '卡密什么时候发放？',
    answer: '卡密会在支付成功后立即自动发放：\n• 支付完成后，系统会立即处理您的订单\n• 卡密会发送到您提供的邮箱地址\n• 同时您也可以在订单详情页面查看卡密\n• 如果5分钟内未收到，请检查垃圾邮件文件夹',
    category: '卡密发放'
  },
  {
    id: '5',
    question: '卡密无法使用怎么办？',
    answer: '如果卡密无法使用，请按以下步骤处理：\n1. 确认卡密输入正确，注意大小写\n2. 检查卡密是否已过期\n3. 确认在正确的平台使用卡密\n4. 如果仍有问题，请联系客服并提供订单号',
    category: '售后服务'
  },
  {
    id: '6',
    question: '可以申请退款吗？',
    answer: '由于数字商品的特殊性，一般情况下不支持退款。但在以下情况下可以申请退款：\n• 卡密无效或已被使用\n• 系统错误导致的重复扣费\n• 商品描述与实际不符\n请联系客服并提供相关证明材料。',
    category: '售后服务'
  }
]

const categories = ['全部', '购买流程', '支付相关', '订单管理', '卡密发放', '售后服务']

export default function HelpPage() {
  const [selectedCategory, setSelectedCategory] = useState('全部')
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  const filteredFAQ = selectedCategory === '全部' 
    ? faqData 
    : faqData.filter(item => item.category === selectedCategory)

  const toggleExpanded = (id: string) => {
    setExpandedItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="text-xl font-bold text-gray-900">
                自动发卡网站
              </Link>
            </div>

            <div className="flex items-center space-x-4">
              <Link href="/" className="text-gray-700 hover:text-gray-900 transition-colors">
                首页
              </Link>
              <Link href="/orders" className="text-gray-700 hover:text-gray-900 transition-colors">
                订单查询
              </Link>
              <Link href="/help" className="text-blue-600 font-medium">
                帮助中心
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">帮助中心</h1>
          <p className="text-xl text-gray-600">
            为您提供详细的使用指南和常见问题解答
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* 侧边栏 - 快速导航 */}
          <div className="lg:col-span-1">
            <Card className="sticky top-24">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Search className="w-5 h-5 mr-2" />
                  快速导航
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Link href="#purchase-guide" className="flex items-center p-2 rounded-lg hover:bg-gray-50 transition-colors">
                  <ShoppingCart className="w-4 h-4 mr-2 text-blue-500" />
                  购买指南
                </Link>
                <Link href="#order-management" className="flex items-center p-2 rounded-lg hover:bg-gray-50 transition-colors">
                  <CreditCard className="w-4 h-4 mr-2 text-green-500" />
                  订单管理
                </Link>
                <Link href="#faq" className="flex items-center p-2 rounded-lg hover:bg-gray-50 transition-colors">
                  <MessageCircle className="w-4 h-4 mr-2 text-purple-500" />
                  常见问题
                </Link>
                <Link href="#contact" className="flex items-center p-2 rounded-lg hover:bg-gray-50 transition-colors">
                  <Phone className="w-4 h-4 mr-2 text-red-500" />
                  联系我们
                </Link>
              </CardContent>
            </Card>
          </div>

          {/* 主要内容 */}
          <div className="lg:col-span-3 space-y-8">
            {/* 购买指南 */}
            <section id="purchase-guide">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <ShoppingCart className="w-6 h-6 mr-2 text-blue-500" />
                    购买指南
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h3 className="font-semibold text-gray-900">第一步：选择商品</h3>
                      <p className="text-gray-600">
                        浏览我们的商品页面，查看商品详情、价格和库存信息。选择您需要的商品。
                      </p>
                    </div>
                    <div className="space-y-4">
                      <h3 className="font-semibold text-gray-900">第二步：填写信息</h3>
                      <p className="text-gray-600">
                        点击"立即购买"，填写您的邮箱地址。这个邮箱将用于接收卡密和订单信息。
                      </p>
                    </div>
                    <div className="space-y-4">
                      <h3 className="font-semibold text-gray-900">第三步：完成支付</h3>
                      <p className="text-gray-600">
                        选择支付方式，填写支付信息。我们使用安全的 Stripe 支付系统保护您的资金安全。
                      </p>
                    </div>
                    <div className="space-y-4">
                      <h3 className="font-semibold text-gray-900">第四步：获取卡密</h3>
                      <p className="text-gray-600">
                        支付成功后，系统会立即发送卡密到您的邮箱，同时您也可以在订单页面查看。
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </section>

            {/* 订单管理 */}
            <section id="order-management">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <CreditCard className="w-6 h-6 mr-2 text-green-500" />
                    订单管理
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-3">如何查看订单</h3>
                      <div className="bg-gray-50 rounded-lg p-4">
                        <ol className="list-decimal list-inside space-y-2 text-gray-700">
                          <li>点击页面顶部的"订单查询"链接</li>
                          <li>输入您的邮箱地址或订单号</li>
                          <li>点击"查询订单"按钮</li>
                          <li>查看订单详情和卡密信息</li>
                        </ol>
                      </div>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-3">订单状态说明</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="flex items-center space-x-3">
                          <span className="w-3 h-3 bg-yellow-400 rounded-full"></span>
                          <span className="text-gray-700">待支付 - 订单已创建，等待支付</span>
                        </div>
                        <div className="flex items-center space-x-3">
                          <span className="w-3 h-3 bg-green-400 rounded-full"></span>
                          <span className="text-gray-700">已完成 - 支付成功，卡密已发放</span>
                        </div>
                        <div className="flex items-center space-x-3">
                          <span className="w-3 h-3 bg-red-400 rounded-full"></span>
                          <span className="text-gray-700">已取消 - 订单已取消或支付失败</span>
                        </div>
                        <div className="flex items-center space-x-3">
                          <span className="w-3 h-3 bg-gray-400 rounded-full"></span>
                          <span className="text-gray-700">退款中 - 正在处理退款申请</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </section>

            {/* 常见问题 */}
            <section id="faq">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <MessageCircle className="w-6 h-6 mr-2 text-purple-500" />
                    常见问题
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {/* 分类筛选 */}
                  <div className="flex flex-wrap gap-2 mb-6">
                    {categories.map((category) => (
                      <Button
                        key={category}
                        variant={selectedCategory === category ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setSelectedCategory(category)}
                      >
                        {category}
                      </Button>
                    ))}
                  </div>

                  {/* FAQ 列表 */}
                  <div className="space-y-4">
                    {filteredFAQ.map((item) => (
                      <div key={item.id} className="border border-gray-200 rounded-lg">
                        <button
                          onClick={() => toggleExpanded(item.id)}
                          className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 transition-colors"
                        >
                          <span className="font-medium text-gray-900">{item.question}</span>
                          {expandedItems.includes(item.id) ? (
                            <ChevronDown className="w-5 h-5 text-gray-500" />
                          ) : (
                            <ChevronRight className="w-5 h-5 text-gray-500" />
                          )}
                        </button>
                        {expandedItems.includes(item.id) && (
                          <div className="px-4 pb-4">
                            <div className="text-gray-700 whitespace-pre-line">
                              {item.answer}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </section>

            {/* 联系我们 */}
            <section id="contact">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Phone className="w-6 h-6 mr-2 text-red-500" />
                    联系我们
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h3 className="font-semibold text-gray-900">客服邮箱</h3>
                      <div className="flex items-center space-x-3">
                        <Mail className="w-5 h-5 text-blue-500" />
                        <span className="text-gray-700"><EMAIL></span>
                      </div>
                      <p className="text-sm text-gray-600">
                        我们会在24小时内回复您的邮件
                      </p>
                    </div>
                    <div className="space-y-4">
                      <h3 className="font-semibold text-gray-900">工作时间</h3>
                      <div className="text-gray-700">
                        <p>周一至周五：9:00 - 18:00</p>
                        <p>周六至周日：10:00 - 16:00</p>
                      </div>
                      <p className="text-sm text-gray-600">
                        节假日可能会有延迟回复
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </section>
          </div>
        </div>
      </div>
    </div>
  )
}
