import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// 获取订单列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    const { searchParams } = new URL(request.url)
    const email = searchParams.get('email')
    const orderId = searchParams.get('orderId')
    const status = searchParams.get('status')
    const page = parseInt(searchParams.get('page') || '1')
    const pageSize = parseInt(searchParams.get('pageSize') || '10')

    let where: any = {}

    // 如果是管理员，可以查看所有订单
    if (session?.user.role === 'ADMIN') {
      if (email) {
        where.email = email
      }
      if (orderId) {
        where.id = orderId
      }
      if (status) {
        where.status = status
      }
    } else {
      // 普通用户只能查看自己的订单
      if (!email) {
        return NextResponse.json(
          { error: '邮箱地址是必需的' },
          { status: 400 }
        )
      }
      where.email = email

      if (orderId) {
        where.id = orderId
      }
      if (status) {
        where.status = status
      }
    }

    // 计算分页参数
    const skip = (page - 1) * pageSize
    const take = pageSize

    // 获取总数
    const total = await prisma.order.count({ where })

    // 获取分页数据
    const orders = await prisma.order.findMany({
      where,
      include: {
        orderItems: {
          include: {
            product: {
              select: {
                name: true,
                image: true
              }
            }
          }
        },
        user: session?.user.role === 'ADMIN' ? {
          select: {
            username: true,
            email: true
          }
        } : false
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take
    })

    return NextResponse.json({
      orders,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    })
  } catch (error) {
    console.error('获取订单错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
