import { promises as fs } from 'fs'
import path from 'path'

const MAINTENANCE_FILE = path.join(process.cwd(), 'maintenance.json')

interface MaintenanceSettings {
  maintenanceMode: boolean
  maintenanceMessage: string
  updatedAt: string
}

// 检查是否处于维护模式
export async function isMaintenanceMode(): Promise<boolean> {
  try {
    const data = await fs.readFile(MAINTENANCE_FILE, 'utf8')
    const settings: MaintenanceSettings = JSON.parse(data)
    return settings.maintenanceMode
  } catch (error) {
    // 如果文件不存在或读取失败，默认不是维护模式
    return false
  }
}

// 获取维护消息
export async function getMaintenanceMessage(): Promise<string> {
  try {
    const data = await fs.readFile(MAINTENANCE_FILE, 'utf8')
    const settings: MaintenanceSettings = JSON.parse(data)
    return settings.maintenanceMessage || '网站正在维护中，请稍后再试。'
  } catch (error) {
    return '网站正在维护中，请稍后再试。'
  }
}

// 获取完整的维护设置
export async function getMaintenanceSettings(): Promise<MaintenanceSettings> {
  try {
    const data = await fs.readFile(MAINTENANCE_FILE, 'utf8')
    return JSON.parse(data)
  } catch (error) {
    return {
      maintenanceMode: false,
      maintenanceMessage: '网站正在维护中，请稍后再试。',
      updatedAt: new Date().toISOString()
    }
  }
}
