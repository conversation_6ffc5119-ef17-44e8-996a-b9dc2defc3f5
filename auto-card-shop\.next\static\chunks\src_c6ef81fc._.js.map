{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number) {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(price)\n}\n\nexport function formatDate(date: string | Date) {\n  return new Intl.DateTimeFormat('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/ui/pagination.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { But<PERSON> } from './button'\nimport { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\ninterface PaginationProps {\n  currentPage: number\n  totalPages: number\n  onPageChange: (page: number) => void\n  showSizeChanger?: boolean\n  pageSize?: number\n  onPageSizeChange?: (size: number) => void\n  pageSizeOptions?: number[]\n  className?: string\n}\n\nexport function Pagination({\n  currentPage,\n  totalPages,\n  onPageChange,\n  showSizeChanger = false,\n  pageSize = 10,\n  onPageSizeChange,\n  pageSizeOptions = [10, 20, 50, 100],\n  className\n}: PaginationProps) {\n  // 生成页码数组\n  const generatePageNumbers = () => {\n    const pages: (number | string)[] = []\n    const maxVisiblePages = 7\n\n    if (totalPages <= maxVisiblePages) {\n      // 如果总页数小于等于最大可见页数，显示所有页码\n      for (let i = 1; i <= totalPages; i++) {\n        pages.push(i)\n      }\n    } else {\n      // 总是显示第一页\n      pages.push(1)\n\n      if (currentPage <= 4) {\n        // 当前页在前面时\n        for (let i = 2; i <= 5; i++) {\n          pages.push(i)\n        }\n        pages.push('...')\n        pages.push(totalPages)\n      } else if (currentPage >= totalPages - 3) {\n        // 当前页在后面时\n        pages.push('...')\n        for (let i = totalPages - 4; i <= totalPages; i++) {\n          pages.push(i)\n        }\n      } else {\n        // 当前页在中间时\n        pages.push('...')\n        for (let i = currentPage - 1; i <= currentPage + 1; i++) {\n          pages.push(i)\n        }\n        pages.push('...')\n        pages.push(totalPages)\n      }\n    }\n\n    return pages\n  }\n\n  const pageNumbers = generatePageNumbers()\n\n  const handlePrevious = () => {\n    if (currentPage > 1) {\n      onPageChange(currentPage - 1)\n    }\n  }\n\n  const handleNext = () => {\n    if (currentPage < totalPages) {\n      onPageChange(currentPage + 1)\n    }\n  }\n\n  const handlePageClick = (page: number | string) => {\n    if (typeof page === 'number' && page !== currentPage) {\n      onPageChange(page)\n    }\n  }\n\n  if (totalPages <= 1) {\n    return null\n  }\n\n  return (\n    <div className={cn('flex items-center justify-between', className)}>\n      <div className=\"flex items-center space-x-2\">\n        {showSizeChanger && onPageSizeChange && (\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-sm text-gray-700\">每页显示</span>\n            <select\n              value={pageSize}\n              onChange={(e) => onPageSizeChange(Number(e.target.value))}\n              className=\"px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              {pageSizeOptions.map((size) => (\n                <option key={size} value={size}>\n                  {size}\n                </option>\n              ))}\n            </select>\n            <span className=\"text-sm text-gray-700\">条</span>\n          </div>\n        )}\n      </div>\n\n      <div className=\"flex items-center space-x-1\">\n        {/* 上一页按钮 */}\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={handlePrevious}\n          disabled={currentPage <= 1}\n          className=\"px-2\"\n        >\n          <ChevronLeft className=\"w-4 h-4\" />\n        </Button>\n\n        {/* 页码按钮 */}\n        {pageNumbers.map((page, index) => (\n          <React.Fragment key={index}>\n            {page === '...' ? (\n              <span className=\"px-2 py-1 text-gray-500\">\n                <MoreHorizontal className=\"w-4 h-4\" />\n              </span>\n            ) : (\n              <Button\n                variant={page === currentPage ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => handlePageClick(page)}\n                className={cn(\n                  'px-3 py-1 min-w-[32px]',\n                  page === currentPage && 'bg-blue-600 text-white hover:bg-blue-700'\n                )}\n              >\n                {page}\n              </Button>\n            )}\n          </React.Fragment>\n        ))}\n\n        {/* 下一页按钮 */}\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={handleNext}\n          disabled={currentPage >= totalPages}\n          className=\"px-2\"\n        >\n          <ChevronRight className=\"w-4 h-4\" />\n        </Button>\n      </div>\n\n      <div className=\"text-sm text-gray-700\">\n        第 {currentPage} 页，共 {totalPages} 页\n      </div>\n    </div>\n  )\n}\n\n// 分页信息组件\nexport function PaginationInfo({\n  currentPage,\n  pageSize,\n  total,\n  className\n}: {\n  currentPage: number\n  pageSize: number\n  total: number\n  className?: string\n}) {\n  const start = (currentPage - 1) * pageSize + 1\n  const end = Math.min(currentPage * pageSize, total)\n\n  return (\n    <div className={cn('text-sm text-gray-700', className)}>\n      显示第 {start} 到 {end} 条记录，共 {total} 条\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAkBO,SAAS,WAAW,EACzB,WAAW,EACX,UAAU,EACV,YAAY,EACZ,kBAAkB,KAAK,EACvB,WAAW,EAAE,EACb,gBAAgB,EAChB,kBAAkB;IAAC;IAAI;IAAI;IAAI;CAAI,EACnC,SAAS,EACO;IAChB,SAAS;IACT,MAAM,sBAAsB;QAC1B,MAAM,QAA6B,EAAE;QACrC,MAAM,kBAAkB;QAExB,IAAI,cAAc,iBAAiB;YACjC,yBAAyB;YACzB,IAAK,IAAI,IAAI,GAAG,KAAK,YAAY,IAAK;gBACpC,MAAM,IAAI,CAAC;YACb;QACF,OAAO;YACL,UAAU;YACV,MAAM,IAAI,CAAC;YAEX,IAAI,eAAe,GAAG;gBACpB,UAAU;gBACV,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;oBAC3B,MAAM,IAAI,CAAC;gBACb;gBACA,MAAM,IAAI,CAAC;gBACX,MAAM,IAAI,CAAC;YACb,OAAO,IAAI,eAAe,aAAa,GAAG;gBACxC,UAAU;gBACV,MAAM,IAAI,CAAC;gBACX,IAAK,IAAI,IAAI,aAAa,GAAG,KAAK,YAAY,IAAK;oBACjD,MAAM,IAAI,CAAC;gBACb;YACF,OAAO;gBACL,UAAU;gBACV,MAAM,IAAI,CAAC;gBACX,IAAK,IAAI,IAAI,cAAc,GAAG,KAAK,cAAc,GAAG,IAAK;oBACvD,MAAM,IAAI,CAAC;gBACb;gBACA,MAAM,IAAI,CAAC;gBACX,MAAM,IAAI,CAAC;YACb;QACF;QAEA,OAAO;IACT;IAEA,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,IAAI,cAAc,GAAG;YACnB,aAAa,cAAc;QAC7B;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,cAAc,YAAY;YAC5B,aAAa,cAAc;QAC7B;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,OAAO,SAAS,YAAY,SAAS,aAAa;YACpD,aAAa;QACf;IACF;IAEA,IAAI,cAAc,GAAG;QACnB,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;;0BACtD,6LAAC;gBAAI,WAAU;0BACZ,mBAAmB,kCAClB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;sCAAwB;;;;;;sCACxC,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,iBAAiB,OAAO,EAAE,MAAM,CAAC,KAAK;4BACvD,WAAU;sCAET,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC;oCAAkB,OAAO;8CACvB;mCADU;;;;;;;;;;sCAKjB,6LAAC;4BAAK,WAAU;sCAAwB;;;;;;;;;;;;;;;;;0BAK9C,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU,eAAe;wBACzB,WAAU;kCAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;oBAIxB,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;sCACZ,SAAS,sBACR,6LAAC;gCAAK,WAAU;0CACd,cAAA,6LAAC,mNAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;qDAG5B,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS,SAAS,cAAc,YAAY;gCAC5C,MAAK;gCACL,SAAS,IAAM,gBAAgB;gCAC/B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0BACA,SAAS,eAAe;0CAGzB;;;;;;2BAfc;;;;;kCAsBvB,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU,eAAe;wBACzB,WAAU;kCAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAI5B,6LAAC;gBAAI,WAAU;;oBAAwB;oBAClC;oBAAY;oBAAM;oBAAW;;;;;;;;;;;;;AAIxC;KArJgB;AAwJT,SAAS,eAAe,EAC7B,WAAW,EACX,QAAQ,EACR,KAAK,EACL,SAAS,EAMV;IACC,MAAM,QAAQ,CAAC,cAAc,CAAC,IAAI,WAAW;IAC7C,MAAM,MAAM,KAAK,GAAG,CAAC,cAAc,UAAU;IAE7C,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;;YAAY;YACjD;YAAM;YAAI;YAAI;YAAQ;YAAM;;;;;;;AAGvC;MAnBgB", "debugId": null}}, {"offset": {"line": 368, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/app/admin/orders/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Pagination, PaginationInfo } from '@/components/ui/pagination'\nimport { formatPrice, formatDate } from '@/lib/utils'\nimport { Eye, Download, RefreshCw, Search } from 'lucide-react'\nimport Link from 'next/link'\n\ninterface Order {\n  id: string\n  email: string\n  status: string\n  totalAmount: number\n  stripePaymentId: string | null\n  createdAt: string\n  updatedAt: string\n  user?: {\n    username: string\n    email: string\n  }\n  orderItems: Array<{\n    id: string\n    quantity: number\n    price: number\n    product: {\n      name: string\n      image: string | null\n    }\n  }>\n}\n\nexport default function OrdersManagement() {\n  const [orders, setOrders] = useState<Order[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchEmail, setSearchEmail] = useState('')\n  const [searchOrderId, setSearchOrderId] = useState('')\n  const [statusFilter, setStatusFilter] = useState('')\n  const [currentPage, setCurrentPage] = useState(1)\n  const [pageSize, setPageSize] = useState(10)\n  const [pagination, setPagination] = useState({\n    page: 1,\n    pageSize: 10,\n    total: 0,\n    totalPages: 0\n  })\n\n  useEffect(() => {\n    fetchOrders()\n  }, [])\n\n  // 当筛选条件或分页改变时自动搜索\n  useEffect(() => {\n    fetchOrders()\n  }, [currentPage, pageSize])\n\n  useEffect(() => {\n    if (searchEmail || searchOrderId || statusFilter) {\n      setCurrentPage(1) // 重置到第一页\n      fetchOrders()\n    }\n  }, [searchEmail, searchOrderId, statusFilter])\n\n  const fetchOrders = async () => {\n    try {\n      const params = new URLSearchParams()\n      if (searchEmail) params.append('email', searchEmail)\n      if (searchOrderId) params.append('orderId', searchOrderId)\n      if (statusFilter) params.append('status', statusFilter)\n      params.append('page', currentPage.toString())\n      params.append('pageSize', pageSize.toString())\n\n      const response = await fetch(`/api/orders?${params}`)\n      const data = await response.json()\n      setOrders(data.orders || [])\n      setPagination(data.pagination || {\n        page: 1,\n        pageSize: 10,\n        total: 0,\n        totalPages: 0\n      })\n    } catch (error) {\n      console.error('获取订单失败:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleSearch = () => {\n    setLoading(true)\n    fetchOrders()\n  }\n\n  const handleClearSearch = () => {\n    setSearchEmail('')\n    setSearchOrderId('')\n    setStatusFilter('')\n    setCurrentPage(1)\n    setLoading(true)\n    fetchOrders()\n  }\n\n  const handlePageChange = (page: number) => {\n    setCurrentPage(page)\n  }\n\n  const handlePageSizeChange = (size: number) => {\n    setPageSize(size)\n    setCurrentPage(1)\n  }\n\n  const updateOrderStatus = async (orderId: string, newStatus: string) => {\n    if (!confirm(`确定要将订单状态更改为 ${getStatusText(newStatus)} 吗？`)) return\n\n    try {\n      const response = await fetch(`/api/orders/${orderId}`, {\n        method: 'PATCH',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ status: newStatus }),\n      })\n\n      if (response.ok) {\n        await fetchOrders()\n        alert('订单状态更新成功')\n      } else {\n        alert('更新失败')\n      }\n    } catch (error) {\n      alert('更新失败，请重试')\n    }\n  }\n\n  const exportOrders = () => {\n    if (orders.length === 0) {\n      alert('没有订单可导出')\n      return\n    }\n\n    let content = '订单号,邮箱,状态,总金额,支付ID,商品信息,创建时间\\n'\n    \n    orders.forEach(order => {\n      const products = order.orderItems.map(item => \n        `${item.product.name} x${item.quantity}`\n      ).join('; ')\n      \n      content += `\"${order.id}\",\"${order.email}\",\"${getStatusText(order.status)}\",\"${order.totalAmount}\",\"${order.stripePaymentId || ''}\",\"${products}\",\"${formatDate(order.createdAt)}\"\\n`\n    })\n\n    const blob = new Blob([content], { type: 'text/csv;charset=utf-8' })\n    const url = URL.createObjectURL(blob)\n    const a = document.createElement('a')\n    a.href = url\n    a.download = `orders-${new Date().toISOString().split('T')[0]}.csv`\n    document.body.appendChild(a)\n    a.click()\n    document.body.removeChild(a)\n    URL.revokeObjectURL(url)\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'PENDING':\n        return 'bg-yellow-100 text-yellow-800'\n      case 'PAID':\n        return 'bg-blue-100 text-blue-800'\n      case 'DELIVERED':\n        return 'bg-green-100 text-green-800'\n      case 'CANCELLED':\n        return 'bg-red-100 text-red-800'\n      case 'REFUNDED':\n        return 'bg-gray-100 text-gray-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'PENDING':\n        return '待支付'\n      case 'PAID':\n        return '已支付'\n      case 'DELIVERED':\n        return '已交付'\n      case 'CANCELLED':\n        return '已取消'\n      case 'REFUNDED':\n        return '已退款'\n      default:\n        return status\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"text-gray-500\">加载中...</div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">订单管理</h1>\n          <p className=\"text-gray-600\">查看和管理所有订单</p>\n        </div>\n        <div className=\"flex space-x-3\">\n          <Button variant=\"outline\" onClick={exportOrders}>\n            <Download className=\"w-4 h-4 mr-2\" />\n            导出订单\n          </Button>\n          <Button variant=\"outline\" onClick={fetchOrders}>\n            <RefreshCw className=\"w-4 h-4 mr-2\" />\n            刷新\n          </Button>\n        </div>\n      </div>\n\n      {/* 搜索和筛选 */}\n      <div className=\"bg-white rounded-lg shadow p-4\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 items-end\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              邮箱搜索\n            </label>\n            <input\n              type=\"email\"\n              value={searchEmail}\n              onChange={(e) => setSearchEmail(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n              placeholder=\"输入邮箱地址\"\n            />\n          </div>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              订单号搜索\n            </label>\n            <input\n              type=\"text\"\n              value={searchOrderId}\n              onChange={(e) => setSearchOrderId(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n              placeholder=\"输入订单号\"\n            />\n          </div>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              状态筛选\n            </label>\n            <select\n              value={statusFilter}\n              onChange={(e) => setStatusFilter(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n            >\n              <option value=\"\">所有状态</option>\n              <option value=\"PENDING\">待支付</option>\n              <option value=\"PAID\">已支付</option>\n              <option value=\"DELIVERED\">已交付</option>\n              <option value=\"CANCELLED\">已取消</option>\n              <option value=\"REFUNDED\">已退款</option>\n            </select>\n          </div>\n          \n          <div className=\"flex space-x-2\">\n            <Button onClick={handleSearch}>\n              <Search className=\"w-4 h-4 mr-2\" />\n              搜索\n            </Button>\n            <Button variant=\"outline\" onClick={handleClearSearch}>\n              清空\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* 统计信息 */}\n      <div className=\"grid grid-cols-1 md:grid-cols-5 gap-4\">\n        {['PENDING', 'PAID', 'DELIVERED', 'CANCELLED', 'REFUNDED'].map(status => {\n          const count = orders.filter(order => order.status === status).length\n          const total = orders.filter(order => order.status === status)\n            .reduce((sum, order) => sum + order.totalAmount, 0)\n          \n          return (\n            <div key={status} className=\"bg-white rounded-lg shadow p-4\">\n              <div className=\"text-sm text-gray-600\">{getStatusText(status)}</div>\n              <div className=\"text-2xl font-bold text-gray-900\">{count}</div>\n              <div className=\"text-sm text-gray-500\">{formatPrice(total)}</div>\n            </div>\n          )\n        })}\n      </div>\n\n      {/* 订单列表 */}\n      <div className=\"bg-white rounded-lg shadow overflow-hidden\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  订单信息\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  客户\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  商品\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  金额\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  状态\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  时间\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  操作\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {orders.map((order) => (\n                <tr key={order.id} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div>\n                      <div className=\"text-sm font-medium text-gray-900 font-mono\">\n                        {order.id.slice(0, 8)}...\n                      </div>\n                      {order.stripePaymentId && (\n                        <div className=\"text-xs text-gray-500\">\n                          Stripe: {order.stripePaymentId.slice(0, 12)}...\n                        </div>\n                      )}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div>\n                      <div className=\"text-sm text-gray-900\">{order.email}</div>\n                      {order.user && (\n                        <div className=\"text-xs text-gray-500\">{order.user.username}</div>\n                      )}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4\">\n                    <div className=\"space-y-1\">\n                      {order.orderItems.map((item, index) => (\n                        <div key={index} className=\"text-sm text-gray-900\">\n                          {item.product.name} × {item.quantity}\n                        </div>\n                      ))}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600\">\n                    {formatPrice(order.totalAmount)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"space-y-2\">\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>\n                        {getStatusText(order.status)}\n                      </span>\n                      {order.status === 'PAID' && (\n                        <div>\n                          <Button\n                            size=\"sm\"\n                            variant=\"outline\"\n                            onClick={() => updateOrderStatus(order.id, 'DELIVERED')}\n                          >\n                            标记已交付\n                          </Button>\n                        </div>\n                      )}\n                      {order.status === 'PENDING' && (\n                        <div>\n                          <Button\n                            size=\"sm\"\n                            variant=\"outline\"\n                            onClick={() => updateOrderStatus(order.id, 'CANCELLED')}\n                          >\n                            取消订单\n                          </Button>\n                        </div>\n                      )}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    <div>\n                      <div>创建: {formatDate(order.createdAt)}</div>\n                      {order.updatedAt !== order.createdAt && (\n                        <div>更新: {formatDate(order.updatedAt)}</div>\n                      )}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    {order.status === 'DELIVERED' && (\n                      <Link href={`/orders/${order.id}/cards`}>\n                        <Button size=\"sm\" variant=\"outline\">\n                          <Eye className=\"w-4 h-4 mr-1\" />\n                          查看卡密\n                        </Button>\n                      </Link>\n                    )}\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n\n        {/* 分页组件 */}\n        {orders.length > 0 && (\n          <div className=\"mt-6 flex flex-col sm:flex-row justify-between items-center gap-4\">\n            <PaginationInfo\n              currentPage={pagination.page}\n              pageSize={pagination.pageSize}\n              total={pagination.total}\n            />\n            <Pagination\n              currentPage={pagination.page}\n              totalPages={pagination.totalPages}\n              onPageChange={handlePageChange}\n              showSizeChanger={true}\n              pageSize={pagination.pageSize}\n              onPageSizeChange={handlePageSizeChange}\n              pageSizeOptions={[10, 20, 50, 100]}\n            />\n          </div>\n        )}\n      </div>\n\n      {orders.length === 0 && !loading && (\n        <div className=\"text-center py-12\">\n          <div className=\"text-gray-500\">暂无订单</div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AAPA;;;;;;;AAgCe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,MAAM;QACN,UAAU;QACV,OAAO;QACP,YAAY;IACd;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;QACF;qCAAG,EAAE;IAEL,kBAAkB;IAClB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;QACF;qCAAG;QAAC;QAAa;KAAS;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,eAAe,iBAAiB,cAAc;gBAChD,eAAe,GAAG,SAAS;;gBAC3B;YACF;QACF;qCAAG;QAAC;QAAa;QAAe;KAAa;IAE7C,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,SAAS,IAAI;YACnB,IAAI,aAAa,OAAO,MAAM,CAAC,SAAS;YACxC,IAAI,eAAe,OAAO,MAAM,CAAC,WAAW;YAC5C,IAAI,cAAc,OAAO,MAAM,CAAC,UAAU;YAC1C,OAAO,MAAM,CAAC,QAAQ,YAAY,QAAQ;YAC1C,OAAO,MAAM,CAAC,YAAY,SAAS,QAAQ;YAE3C,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,QAAQ;YACpD,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,UAAU,KAAK,MAAM,IAAI,EAAE;YAC3B,cAAc,KAAK,UAAU,IAAI;gBAC/B,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,YAAY;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,WAAW;QACX;IACF;IAEA,MAAM,oBAAoB;QACxB,eAAe;QACf,iBAAiB;QACjB,gBAAgB;QAChB,eAAe;QACf,WAAW;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,eAAe;IACjB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,YAAY;QACZ,eAAe;IACjB;IAEA,MAAM,oBAAoB,OAAO,SAAiB;QAChD,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,cAAc,WAAW,GAAG,CAAC,GAAG;QAE5D,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,SAAS,EAAE;gBACrD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,QAAQ;gBAAU;YAC3C;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;gBACN,MAAM;YACR,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,OAAO,MAAM,KAAK,GAAG;YACvB,MAAM;YACN;QACF;QAEA,IAAI,UAAU;QAEd,OAAO,OAAO,CAAC,CAAA;YACb,MAAM,WAAW,MAAM,UAAU,CAAC,GAAG,CAAC,CAAA,OACpC,GAAG,KAAK,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,QAAQ,EAAE,EACxC,IAAI,CAAC;YAEP,WAAW,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,CAAC,GAAG,EAAE,cAAc,MAAM,MAAM,EAAE,GAAG,EAAE,MAAM,WAAW,CAAC,GAAG,EAAE,MAAM,eAAe,IAAI,GAAG,GAAG,EAAE,SAAS,GAAG,EAAE,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,SAAS,EAAE,GAAG,CAAC;QACvL;QAEA,MAAM,OAAO,IAAI,KAAK;YAAC;SAAQ,EAAE;YAAE,MAAM;QAAyB;QAClE,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,OAAO,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;QACnE,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAgB;;;;;;;;;;;IAGrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAE/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;;kDACjC,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;;kDACjC,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO5C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;oCAChD,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAC/C,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,6LAAC;4CAAO,OAAM;sDAAU;;;;;;sDACxB,6LAAC;4CAAO,OAAM;sDAAO;;;;;;sDACrB,6LAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,6LAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,6LAAC;4CAAO,OAAM;sDAAW;;;;;;;;;;;;;;;;;;sCAI7B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;;sDACf,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGrC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;8CAAmB;;;;;;;;;;;;;;;;;;;;;;;0BAQ5D,6LAAC;gBAAI,WAAU;0BACZ;oBAAC;oBAAW;oBAAQ;oBAAa;oBAAa;iBAAW,CAAC,GAAG,CAAC,CAAA;oBAC7D,MAAM,QAAQ,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,QAAQ,MAAM;oBACpE,MAAM,QAAQ,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,QACnD,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,WAAW,EAAE;oBAEnD,qBACE,6LAAC;wBAAiB,WAAU;;0CAC1B,6LAAC;gCAAI,WAAU;0CAAyB,cAAc;;;;;;0CACtD,6LAAC;gCAAI,WAAU;0CAAoC;;;;;;0CACnD,6LAAC;gCAAI,WAAU;0CAAyB,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;;;;;;;uBAH5C;;;;;gBAMd;;;;;;0BAIF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCAAM,WAAU;8CACf,cAAA,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;;;;;;;;;;;;8CAKnG,6LAAC;oCAAM,WAAU;8CACd,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;4CAAkB,WAAU;;8DAC3B,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;;oEACZ,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG;oEAAG;;;;;;;4DAEvB,MAAM,eAAe,kBACpB,6LAAC;gEAAI,WAAU;;oEAAwB;oEAC5B,MAAM,eAAe,CAAC,KAAK,CAAC,GAAG;oEAAI;;;;;;;;;;;;;;;;;;8DAKpD,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;0EAAyB,MAAM,KAAK;;;;;;4DAClD,MAAM,IAAI,kBACT,6LAAC;gEAAI,WAAU;0EAAyB,MAAM,IAAI,CAAC,QAAQ;;;;;;;;;;;;;;;;;8DAIjE,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAI,WAAU;kEACZ,MAAM,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC3B,6LAAC;gEAAgB,WAAU;;oEACxB,KAAK,OAAO,CAAC,IAAI;oEAAC;oEAAI,KAAK,QAAQ;;+DAD5B;;;;;;;;;;;;;;;8DAMhB,6LAAC;oDAAG,WAAU;8DACX,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,MAAM,WAAW;;;;;;8DAEhC,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,MAAM,MAAM,GAAG;0EAC1F,cAAc,MAAM,MAAM;;;;;;4DAE5B,MAAM,MAAM,KAAK,wBAChB,6LAAC;0EACC,cAAA,6LAAC,qIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,SAAS,IAAM,kBAAkB,MAAM,EAAE,EAAE;8EAC5C;;;;;;;;;;;4DAKJ,MAAM,MAAM,KAAK,2BAChB,6LAAC;0EACC,cAAA,6LAAC,qIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,SAAS,IAAM,kBAAkB,MAAM,EAAE,EAAE;8EAC5C;;;;;;;;;;;;;;;;;;;;;;8DAOT,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;;0EACC,6LAAC;;oEAAI;oEAAK,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,SAAS;;;;;;;4DACnC,MAAM,SAAS,KAAK,MAAM,SAAS,kBAClC,6LAAC;;oEAAI;oEAAK,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,SAAS;;;;;;;;;;;;;;;;;;8DAI1C,6LAAC;oDAAG,WAAU;8DACX,MAAM,MAAM,KAAK,6BAChB,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC;kEACrC,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,MAAK;4DAAK,SAAQ;;8EACxB,6LAAC,mMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;2CA1EjC,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;oBAuFxB,OAAO,MAAM,GAAG,mBACf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yIAAA,CAAA,iBAAc;gCACb,aAAa,WAAW,IAAI;gCAC5B,UAAU,WAAW,QAAQ;gCAC7B,OAAO,WAAW,KAAK;;;;;;0CAEzB,6LAAC,yIAAA,CAAA,aAAU;gCACT,aAAa,WAAW,IAAI;gCAC5B,YAAY,WAAW,UAAU;gCACjC,cAAc;gCACd,iBAAiB;gCACjB,UAAU,WAAW,QAAQ;gCAC7B,kBAAkB;gCAClB,iBAAiB;oCAAC;oCAAI;oCAAI;oCAAI;iCAAI;;;;;;;;;;;;;;;;;;YAMzC,OAAO,MAAM,KAAK,KAAK,CAAC,yBACvB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BAAgB;;;;;;;;;;;;;;;;;AAKzC;GA3ZwB;KAAA", "debugId": null}}]}